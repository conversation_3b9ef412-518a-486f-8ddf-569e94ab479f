import {
  require_react
} from "./chunk-UGC3UZ7L.js";
import {
  __commonJS
} from "./chunk-G3PMV62Z.js";

// node_modules/lodash.debounce/index.js
var require_lodash = __commonJS({
  "node_modules/lodash.debounce/index.js"(exports, module) {
    var FUNC_ERROR_TEXT = "Expected a function";
    var NAN = 0 / 0;
    var symbolTag = "[object Symbol]";
    var reTrim = /^\s+|\s+$/g;
    var reIsBadHex = /^[-+]0x[0-9a-f]+$/i;
    var reIsBinary = /^0b[01]+$/i;
    var reIsOctal = /^0o[0-7]+$/i;
    var freeParseInt = parseInt;
    var freeGlobal = typeof global == "object" && global && global.Object === Object && global;
    var freeSelf = typeof self == "object" && self && self.Object === Object && self;
    var root = freeGlobal || freeSelf || Function("return this")();
    var objectProto = Object.prototype;
    var objectToString = objectProto.toString;
    var nativeMax = Math.max;
    var nativeMin = Math.min;
    var now = function() {
      return root.Date.now();
    };
    function debounce(func, wait, options) {
      var lastArgs, lastThis, maxWait, result, timerId, lastCallTime, lastInvokeTime = 0, leading = false, maxing = false, trailing = true;
      if (typeof func != "function") {
        throw new TypeError(FUNC_ERROR_TEXT);
      }
      wait = toNumber(wait) || 0;
      if (isObject(options)) {
        leading = !!options.leading;
        maxing = "maxWait" in options;
        maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;
        trailing = "trailing" in options ? !!options.trailing : trailing;
      }
      function invokeFunc(time) {
        var args = lastArgs, thisArg = lastThis;
        lastArgs = lastThis = void 0;
        lastInvokeTime = time;
        result = func.apply(thisArg, args);
        return result;
      }
      function leadingEdge(time) {
        lastInvokeTime = time;
        timerId = setTimeout(timerExpired, wait);
        return leading ? invokeFunc(time) : result;
      }
      function remainingWait(time) {
        var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime, result2 = wait - timeSinceLastCall;
        return maxing ? nativeMin(result2, maxWait - timeSinceLastInvoke) : result2;
      }
      function shouldInvoke(time) {
        var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime;
        return lastCallTime === void 0 || timeSinceLastCall >= wait || timeSinceLastCall < 0 || maxing && timeSinceLastInvoke >= maxWait;
      }
      function timerExpired() {
        var time = now();
        if (shouldInvoke(time)) {
          return trailingEdge(time);
        }
        timerId = setTimeout(timerExpired, remainingWait(time));
      }
      function trailingEdge(time) {
        timerId = void 0;
        if (trailing && lastArgs) {
          return invokeFunc(time);
        }
        lastArgs = lastThis = void 0;
        return result;
      }
      function cancel() {
        if (timerId !== void 0) {
          clearTimeout(timerId);
        }
        lastInvokeTime = 0;
        lastArgs = lastCallTime = lastThis = timerId = void 0;
      }
      function flush() {
        return timerId === void 0 ? result : trailingEdge(now());
      }
      function debounced() {
        var time = now(), isInvoking = shouldInvoke(time);
        lastArgs = arguments;
        lastThis = this;
        lastCallTime = time;
        if (isInvoking) {
          if (timerId === void 0) {
            return leadingEdge(lastCallTime);
          }
          if (maxing) {
            timerId = setTimeout(timerExpired, wait);
            return invokeFunc(lastCallTime);
          }
        }
        if (timerId === void 0) {
          timerId = setTimeout(timerExpired, wait);
        }
        return result;
      }
      debounced.cancel = cancel;
      debounced.flush = flush;
      return debounced;
    }
    function isObject(value) {
      var type = typeof value;
      return !!value && (type == "object" || type == "function");
    }
    function isObjectLike(value) {
      return !!value && typeof value == "object";
    }
    function isSymbol(value) {
      return typeof value == "symbol" || isObjectLike(value) && objectToString.call(value) == symbolTag;
    }
    function toNumber(value) {
      if (typeof value == "number") {
        return value;
      }
      if (isSymbol(value)) {
        return NAN;
      }
      if (isObject(value)) {
        var other = typeof value.valueOf == "function" ? value.valueOf() : value;
        value = isObject(other) ? other + "" : other;
      }
      if (typeof value != "string") {
        return value === 0 ? value : +value;
      }
      value = value.replace(reTrim, "");
      var isBinary = reIsBinary.test(value);
      return isBinary || reIsOctal.test(value) ? freeParseInt(value.slice(2), isBinary ? 2 : 8) : reIsBadHex.test(value) ? NAN : +value;
    }
    module.exports = debounce;
  }
});

// node_modules/react-debounce-input/lib/Component.js
var require_Component = __commonJS({
  "node_modules/react-debounce-input/lib/Component.js"(exports) {
    "use strict";
    function _typeof(obj) {
      "@babel/helpers - typeof";
      return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(obj2) {
        return typeof obj2;
      } : function(obj2) {
        return obj2 && "function" == typeof Symbol && obj2.constructor === Symbol && obj2 !== Symbol.prototype ? "symbol" : typeof obj2;
      }, _typeof(obj);
    }
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.DebounceInput = void 0;
    var _react = _interopRequireDefault(require_react());
    var _lodash = _interopRequireDefault(require_lodash());
    var _excluded = ["element", "onChange", "value", "minLength", "debounceTimeout", "forceNotifyByEnter", "forceNotifyOnBlur", "onKeyDown", "onBlur", "inputRef"];
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { "default": obj };
    }
    function _objectWithoutProperties(source, excluded) {
      if (source == null) return {};
      var target = _objectWithoutPropertiesLoose(source, excluded);
      var key, i;
      if (Object.getOwnPropertySymbols) {
        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
        for (i = 0; i < sourceSymbolKeys.length; i++) {
          key = sourceSymbolKeys[i];
          if (excluded.indexOf(key) >= 0) continue;
          if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
          target[key] = source[key];
        }
      }
      return target;
    }
    function _objectWithoutPropertiesLoose(source, excluded) {
      if (source == null) return {};
      var target = {};
      var sourceKeys = Object.keys(source);
      var key, i;
      for (i = 0; i < sourceKeys.length; i++) {
        key = sourceKeys[i];
        if (excluded.indexOf(key) >= 0) continue;
        target[key] = source[key];
      }
      return target;
    }
    function ownKeys(object, enumerableOnly) {
      var keys = Object.keys(object);
      if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        enumerableOnly && (symbols = symbols.filter(function(sym) {
          return Object.getOwnPropertyDescriptor(object, sym).enumerable;
        })), keys.push.apply(keys, symbols);
      }
      return keys;
    }
    function _objectSpread(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = null != arguments[i] ? arguments[i] : {};
        i % 2 ? ownKeys(Object(source), true).forEach(function(key) {
          _defineProperty(target, key, source[key]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {
          Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
      }
      return target;
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _defineProperties(target, props) {
      for (var i = 0; i < props.length; i++) {
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, descriptor.key, descriptor);
      }
    }
    function _createClass(Constructor, protoProps, staticProps) {
      if (protoProps) _defineProperties(Constructor.prototype, protoProps);
      if (staticProps) _defineProperties(Constructor, staticProps);
      Object.defineProperty(Constructor, "prototype", { writable: false });
      return Constructor;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } });
      Object.defineProperty(subClass, "prototype", { writable: false });
      if (superClass) _setPrototypeOf(subClass, superClass);
    }
    function _setPrototypeOf(o, p) {
      _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf2(o2, p2) {
        o2.__proto__ = p2;
        return o2;
      };
      return _setPrototypeOf(o, p);
    }
    function _createSuper(Derived) {
      var hasNativeReflectConstruct = _isNativeReflectConstruct();
      return function _createSuperInternal() {
        var Super = _getPrototypeOf(Derived), result;
        if (hasNativeReflectConstruct) {
          var NewTarget = _getPrototypeOf(this).constructor;
          result = Reflect.construct(Super, arguments, NewTarget);
        } else {
          result = Super.apply(this, arguments);
        }
        return _possibleConstructorReturn(this, result);
      };
    }
    function _possibleConstructorReturn(self2, call) {
      if (call && (_typeof(call) === "object" || typeof call === "function")) {
        return call;
      } else if (call !== void 0) {
        throw new TypeError("Derived constructors may only return object or undefined");
      }
      return _assertThisInitialized(self2);
    }
    function _assertThisInitialized(self2) {
      if (self2 === void 0) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return self2;
    }
    function _isNativeReflectConstruct() {
      if (typeof Reflect === "undefined" || !Reflect.construct) return false;
      if (Reflect.construct.sham) return false;
      if (typeof Proxy === "function") return true;
      try {
        Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
        }));
        return true;
      } catch (e) {
        return false;
      }
    }
    function _getPrototypeOf(o) {
      _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf2(o2) {
        return o2.__proto__ || Object.getPrototypeOf(o2);
      };
      return _getPrototypeOf(o);
    }
    function _defineProperty(obj, key, value) {
      if (key in obj) {
        Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
      } else {
        obj[key] = value;
      }
      return obj;
    }
    var DebounceInput = function(_React$PureComponent) {
      _inherits(DebounceInput2, _React$PureComponent);
      var _super = _createSuper(DebounceInput2);
      function DebounceInput2(props) {
        var _this;
        _classCallCheck(this, DebounceInput2);
        _this = _super.call(this, props);
        _defineProperty(_assertThisInitialized(_this), "onChange", function(event) {
          event.persist();
          var oldValue = _this.state.value;
          var minLength = _this.props.minLength;
          _this.setState({
            value: event.target.value
          }, function() {
            var value = _this.state.value;
            if (value.length >= minLength) {
              _this.notify(event);
              return;
            }
            if (oldValue.length > value.length) {
              _this.notify(_objectSpread(_objectSpread({}, event), {}, {
                target: _objectSpread(_objectSpread({}, event.target), {}, {
                  value: ""
                })
              }));
            }
          });
        });
        _defineProperty(_assertThisInitialized(_this), "onKeyDown", function(event) {
          if (event.key === "Enter") {
            _this.forceNotify(event);
          }
          var onKeyDown = _this.props.onKeyDown;
          if (onKeyDown) {
            event.persist();
            onKeyDown(event);
          }
        });
        _defineProperty(_assertThisInitialized(_this), "onBlur", function(event) {
          _this.forceNotify(event);
          var onBlur = _this.props.onBlur;
          if (onBlur) {
            event.persist();
            onBlur(event);
          }
        });
        _defineProperty(_assertThisInitialized(_this), "createNotifier", function(debounceTimeout) {
          if (debounceTimeout < 0) {
            _this.notify = function() {
              return null;
            };
          } else if (debounceTimeout === 0) {
            _this.notify = _this.doNotify;
          } else {
            var debouncedChangeFunc = (0, _lodash["default"])(function(event) {
              _this.isDebouncing = false;
              _this.doNotify(event);
            }, debounceTimeout);
            _this.notify = function(event) {
              _this.isDebouncing = true;
              debouncedChangeFunc(event);
            };
            _this.flush = function() {
              return debouncedChangeFunc.flush();
            };
            _this.cancel = function() {
              _this.isDebouncing = false;
              debouncedChangeFunc.cancel();
            };
          }
        });
        _defineProperty(_assertThisInitialized(_this), "doNotify", function() {
          var onChange = _this.props.onChange;
          onChange.apply(void 0, arguments);
        });
        _defineProperty(_assertThisInitialized(_this), "forceNotify", function(event) {
          var debounceTimeout = _this.props.debounceTimeout;
          if (!_this.isDebouncing && debounceTimeout > 0) {
            return;
          }
          if (_this.cancel) {
            _this.cancel();
          }
          var value = _this.state.value;
          var minLength = _this.props.minLength;
          if (value.length >= minLength) {
            _this.doNotify(event);
          } else {
            _this.doNotify(_objectSpread(_objectSpread({}, event), {}, {
              target: _objectSpread(_objectSpread({}, event.target), {}, {
                value
              })
            }));
          }
        });
        _this.isDebouncing = false;
        _this.state = {
          value: typeof props.value === "undefined" || props.value === null ? "" : props.value
        };
        var _debounceTimeout2 = _this.props.debounceTimeout;
        _this.createNotifier(_debounceTimeout2);
        return _this;
      }
      _createClass(DebounceInput2, [{
        key: "componentDidUpdate",
        value: function componentDidUpdate(prevProps) {
          if (this.isDebouncing) {
            return;
          }
          var _this$props = this.props, value = _this$props.value, debounceTimeout = _this$props.debounceTimeout;
          var oldTimeout = prevProps.debounceTimeout, oldValue = prevProps.value;
          var stateValue = this.state.value;
          if (typeof value !== "undefined" && oldValue !== value && stateValue !== value) {
            this.setState({
              value
            });
          }
          if (debounceTimeout !== oldTimeout) {
            this.createNotifier(debounceTimeout);
          }
        }
      }, {
        key: "componentWillUnmount",
        value: function componentWillUnmount() {
          if (this.flush) {
            this.flush();
          }
        }
      }, {
        key: "render",
        value: function render() {
          var _this$props2 = this.props, element = _this$props2.element, _onChange = _this$props2.onChange, _value = _this$props2.value, _minLength = _this$props2.minLength, _debounceTimeout = _this$props2.debounceTimeout, forceNotifyByEnter = _this$props2.forceNotifyByEnter, forceNotifyOnBlur = _this$props2.forceNotifyOnBlur, onKeyDown = _this$props2.onKeyDown, onBlur = _this$props2.onBlur, inputRef = _this$props2.inputRef, props = _objectWithoutProperties(_this$props2, _excluded);
          var value = this.state.value;
          var maybeOnKeyDown;
          if (forceNotifyByEnter) {
            maybeOnKeyDown = {
              onKeyDown: this.onKeyDown
            };
          } else if (onKeyDown) {
            maybeOnKeyDown = {
              onKeyDown
            };
          } else {
            maybeOnKeyDown = {};
          }
          var maybeOnBlur;
          if (forceNotifyOnBlur) {
            maybeOnBlur = {
              onBlur: this.onBlur
            };
          } else if (onBlur) {
            maybeOnBlur = {
              onBlur
            };
          } else {
            maybeOnBlur = {};
          }
          var maybeRef = inputRef ? {
            ref: inputRef
          } : {};
          return _react["default"].createElement(element, _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, props), {}, {
            onChange: this.onChange,
            value
          }, maybeOnKeyDown), maybeOnBlur), maybeRef));
        }
      }]);
      return DebounceInput2;
    }(_react["default"].PureComponent);
    exports.DebounceInput = DebounceInput;
    _defineProperty(DebounceInput, "defaultProps", {
      element: "input",
      type: "text",
      onKeyDown: void 0,
      onBlur: void 0,
      value: void 0,
      minLength: 0,
      debounceTimeout: 100,
      forceNotifyByEnter: true,
      forceNotifyOnBlur: true,
      inputRef: void 0
    });
  }
});

// node_modules/react-debounce-input/lib/index.js
var require_lib = __commonJS({
  "node_modules/react-debounce-input/lib/index.js"(exports, module) {
    var _require = require_Component();
    var DebounceInput = _require.DebounceInput;
    DebounceInput.DebounceInput = DebounceInput;
    module.exports = DebounceInput;
  }
});
export default require_lib();
//# sourceMappingURL=react-debounce-input.js.map
