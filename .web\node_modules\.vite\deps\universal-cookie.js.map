{"version": 3, "sources": ["../../universal-cookie/esm/index.mjs"], "sourcesContent": ["var cookie = {};\n\n/*!\n * cookie\n * Copyright(c) 2012-2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\nvar hasRequiredCookie;\n\nfunction requireCookie () {\n\tif (hasRequiredCookie) return cookie;\n\thasRequiredCookie = 1;\n\n\t/**\n\t * Module exports.\n\t * @public\n\t */\n\n\tcookie.parse = parse;\n\tcookie.serialize = serialize;\n\n\t/**\n\t * Module variables.\n\t * @private\n\t */\n\n\tvar __toString = Object.prototype.toString;\n\tvar __hasOwnProperty = Object.prototype.hasOwnProperty;\n\n\t/**\n\t * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n\t * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n\t * which has been replaced by the token definition in RFC 7230 appendix B.\n\t *\n\t * cookie-name       = token\n\t * token             = 1*tchar\n\t * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n\t *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n\t *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n\t */\n\n\tvar cookieNameRegExp = /^[!#$%&'*+\\-.^_`|~0-9A-Za-z]+$/;\n\n\t/**\n\t * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n\t *\n\t * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n\t * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n\t *                     ; US-ASCII characters excluding CTLs,\n\t *                     ; whitespace DQUOTE, comma, semicolon,\n\t *                     ; and backslash\n\t */\n\n\tvar cookieValueRegExp = /^(\"?)[\\u0021\\u0023-\\u002B\\u002D-\\u003A\\u003C-\\u005B\\u005D-\\u007E]*\\1$/;\n\n\t/**\n\t * RegExp to match domain-value in RFC 6265 sec 4.1.1\n\t *\n\t * domain-value      = <subdomain>\n\t *                     ; defined in [RFC1034], Section 3.5, as\n\t *                     ; enhanced by [RFC1123], Section 2.1\n\t * <subdomain>       = <label> | <subdomain> \".\" <label>\n\t * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n\t *                     Labels must be 63 characters or less.\n\t *                     'let-dig' not 'letter' in the first char, per RFC1123\n\t * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n\t * <let-dig-hyp>     = <let-dig> | \"-\"\n\t * <let-dig>         = <letter> | <digit>\n\t * <letter>          = any one of the 52 alphabetic characters A through Z in\n\t *                     upper case and a through z in lower case\n\t * <digit>           = any one of the ten digits 0 through 9\n\t *\n\t * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n\t *\n\t * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n\t * character is not permitted, but a trailing %x2E (\".\"), if present, will\n\t * cause the user agent to ignore the attribute.)\n\t */\n\n\tvar domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n\n\t/**\n\t * RegExp to match path-value in RFC 6265 sec 4.1.1\n\t *\n\t * path-value        = <any CHAR except CTLs or \";\">\n\t * CHAR              = %x01-7F\n\t *                     ; defined in RFC 5234 appendix B.1\n\t */\n\n\tvar pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n\n\t/**\n\t * Parse a cookie header.\n\t *\n\t * Parse the given cookie header string into an object\n\t * The object has the various cookies as keys(names) => values\n\t *\n\t * @param {string} str\n\t * @param {object} [opt]\n\t * @return {object}\n\t * @public\n\t */\n\n\tfunction parse(str, opt) {\n\t  if (typeof str !== 'string') {\n\t    throw new TypeError('argument str must be a string');\n\t  }\n\n\t  var obj = {};\n\t  var len = str.length;\n\t  // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n\t  if (len < 2) return obj;\n\n\t  var dec = (opt && opt.decode) || decode;\n\t  var index = 0;\n\t  var eqIdx = 0;\n\t  var endIdx = 0;\n\n\t  do {\n\t    eqIdx = str.indexOf('=', index);\n\t    if (eqIdx === -1) break; // No more cookie pairs.\n\n\t    endIdx = str.indexOf(';', index);\n\n\t    if (endIdx === -1) {\n\t      endIdx = len;\n\t    } else if (eqIdx > endIdx) {\n\t      // backtrack on prior semicolon\n\t      index = str.lastIndexOf(';', eqIdx - 1) + 1;\n\t      continue;\n\t    }\n\n\t    var keyStartIdx = startIndex(str, index, eqIdx);\n\t    var keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n\t    var key = str.slice(keyStartIdx, keyEndIdx);\n\n\t    // only assign once\n\t    if (!__hasOwnProperty.call(obj, key)) {\n\t      var valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n\t      var valEndIdx = endIndex(str, endIdx, valStartIdx);\n\n\t      if (str.charCodeAt(valStartIdx) === 0x22 /* \" */ && str.charCodeAt(valEndIdx - 1) === 0x22 /* \" */) {\n\t        valStartIdx++;\n\t        valEndIdx--;\n\t      }\n\n\t      var val = str.slice(valStartIdx, valEndIdx);\n\t      obj[key] = tryDecode(val, dec);\n\t    }\n\n\t    index = endIdx + 1;\n\t  } while (index < len);\n\n\t  return obj;\n\t}\n\n\tfunction startIndex(str, index, max) {\n\t  do {\n\t    var code = str.charCodeAt(index);\n\t    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index;\n\t  } while (++index < max);\n\t  return max;\n\t}\n\n\tfunction endIndex(str, index, min) {\n\t  while (index > min) {\n\t    var code = str.charCodeAt(--index);\n\t    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index + 1;\n\t  }\n\t  return min;\n\t}\n\n\t/**\n\t * Serialize data into a cookie header.\n\t *\n\t * Serialize a name value pair into a cookie string suitable for\n\t * http headers. An optional options object specifies cookie parameters.\n\t *\n\t * serialize('foo', 'bar', { httpOnly: true })\n\t *   => \"foo=bar; httpOnly\"\n\t *\n\t * @param {string} name\n\t * @param {string} val\n\t * @param {object} [opt]\n\t * @return {string}\n\t * @public\n\t */\n\n\tfunction serialize(name, val, opt) {\n\t  var enc = (opt && opt.encode) || encodeURIComponent;\n\n\t  if (typeof enc !== 'function') {\n\t    throw new TypeError('option encode is invalid');\n\t  }\n\n\t  if (!cookieNameRegExp.test(name)) {\n\t    throw new TypeError('argument name is invalid');\n\t  }\n\n\t  var value = enc(val);\n\n\t  if (!cookieValueRegExp.test(value)) {\n\t    throw new TypeError('argument val is invalid');\n\t  }\n\n\t  var str = name + '=' + value;\n\t  if (!opt) return str;\n\n\t  if (null != opt.maxAge) {\n\t    var maxAge = Math.floor(opt.maxAge);\n\n\t    if (!isFinite(maxAge)) {\n\t      throw new TypeError('option maxAge is invalid')\n\t    }\n\n\t    str += '; Max-Age=' + maxAge;\n\t  }\n\n\t  if (opt.domain) {\n\t    if (!domainValueRegExp.test(opt.domain)) {\n\t      throw new TypeError('option domain is invalid');\n\t    }\n\n\t    str += '; Domain=' + opt.domain;\n\t  }\n\n\t  if (opt.path) {\n\t    if (!pathValueRegExp.test(opt.path)) {\n\t      throw new TypeError('option path is invalid');\n\t    }\n\n\t    str += '; Path=' + opt.path;\n\t  }\n\n\t  if (opt.expires) {\n\t    var expires = opt.expires;\n\n\t    if (!isDate(expires) || isNaN(expires.valueOf())) {\n\t      throw new TypeError('option expires is invalid');\n\t    }\n\n\t    str += '; Expires=' + expires.toUTCString();\n\t  }\n\n\t  if (opt.httpOnly) {\n\t    str += '; HttpOnly';\n\t  }\n\n\t  if (opt.secure) {\n\t    str += '; Secure';\n\t  }\n\n\t  if (opt.partitioned) {\n\t    str += '; Partitioned';\n\t  }\n\n\t  if (opt.priority) {\n\t    var priority = typeof opt.priority === 'string'\n\t      ? opt.priority.toLowerCase() : opt.priority;\n\n\t    switch (priority) {\n\t      case 'low':\n\t        str += '; Priority=Low';\n\t        break\n\t      case 'medium':\n\t        str += '; Priority=Medium';\n\t        break\n\t      case 'high':\n\t        str += '; Priority=High';\n\t        break\n\t      default:\n\t        throw new TypeError('option priority is invalid')\n\t    }\n\t  }\n\n\t  if (opt.sameSite) {\n\t    var sameSite = typeof opt.sameSite === 'string'\n\t      ? opt.sameSite.toLowerCase() : opt.sameSite;\n\n\t    switch (sameSite) {\n\t      case true:\n\t        str += '; SameSite=Strict';\n\t        break;\n\t      case 'lax':\n\t        str += '; SameSite=Lax';\n\t        break;\n\t      case 'strict':\n\t        str += '; SameSite=Strict';\n\t        break;\n\t      case 'none':\n\t        str += '; SameSite=None';\n\t        break;\n\t      default:\n\t        throw new TypeError('option sameSite is invalid');\n\t    }\n\t  }\n\n\t  return str;\n\t}\n\n\t/**\n\t * URL-decode string value. Optimized to skip native call when no %.\n\t *\n\t * @param {string} str\n\t * @returns {string}\n\t */\n\n\tfunction decode (str) {\n\t  return str.indexOf('%') !== -1\n\t    ? decodeURIComponent(str)\n\t    : str\n\t}\n\n\t/**\n\t * Determine if value is a Date.\n\t *\n\t * @param {*} val\n\t * @private\n\t */\n\n\tfunction isDate (val) {\n\t  return __toString.call(val) === '[object Date]';\n\t}\n\n\t/**\n\t * Try decoding a string using a decoding function.\n\t *\n\t * @param {string} str\n\t * @param {function} decode\n\t * @private\n\t */\n\n\tfunction tryDecode(str, decode) {\n\t  try {\n\t    return decode(str);\n\t  } catch (e) {\n\t    return str;\n\t  }\n\t}\n\treturn cookie;\n}\n\nvar cookieExports = requireCookie();\n\nfunction hasDocumentCookie() {\n    const testingValue = typeof global === 'undefined'\n        ? undefined\n        : global.TEST_HAS_DOCUMENT_COOKIE;\n    if (typeof testingValue === 'boolean') {\n        return testingValue;\n    }\n    // Can we get/set cookies on document.cookie?\n    return typeof document === 'object' && typeof document.cookie === 'string';\n}\nfunction parseCookies(cookies) {\n    if (typeof cookies === 'string') {\n        return cookieExports.parse(cookies);\n    }\n    else if (typeof cookies === 'object' && cookies !== null) {\n        return cookies;\n    }\n    else {\n        return {};\n    }\n}\nfunction readCookie(value, options = {}) {\n    const cleanValue = cleanupCookieValue(value);\n    if (!options.doNotParse) {\n        try {\n            return JSON.parse(cleanValue);\n        }\n        catch (e) {\n            // At least we tried\n        }\n    }\n    // Ignore clean value if we failed the deserialization\n    // It is not relevant anymore to trim those values\n    return value;\n}\nfunction cleanupCookieValue(value) {\n    // express prepend j: before serializing a cookie\n    if (value && value[0] === 'j' && value[1] === ':') {\n        return value.substr(2);\n    }\n    return value;\n}\n\nclass Cookies {\n    constructor(cookies, defaultSetOptions = {}) {\n        this.changeListeners = [];\n        this.HAS_DOCUMENT_COOKIE = false;\n        this.update = () => {\n            if (!this.HAS_DOCUMENT_COOKIE) {\n                return;\n            }\n            const previousCookies = this.cookies;\n            this.cookies = cookieExports.parse(document.cookie);\n            this._checkChanges(previousCookies);\n        };\n        const domCookies = typeof document === 'undefined' ? '' : document.cookie;\n        this.cookies = parseCookies(cookies || domCookies);\n        this.defaultSetOptions = defaultSetOptions;\n        this.HAS_DOCUMENT_COOKIE = hasDocumentCookie();\n    }\n    _emitChange(params) {\n        for (let i = 0; i < this.changeListeners.length; ++i) {\n            this.changeListeners[i](params);\n        }\n    }\n    _checkChanges(previousCookies) {\n        const names = new Set(Object.keys(previousCookies).concat(Object.keys(this.cookies)));\n        names.forEach((name) => {\n            if (previousCookies[name] !== this.cookies[name]) {\n                this._emitChange({\n                    name,\n                    value: readCookie(this.cookies[name]),\n                });\n            }\n        });\n    }\n    _startPolling() {\n        this.pollingInterval = setInterval(this.update, 300);\n    }\n    _stopPolling() {\n        if (this.pollingInterval) {\n            clearInterval(this.pollingInterval);\n        }\n    }\n    get(name, options = {}) {\n        if (!options.doNotUpdate) {\n            this.update();\n        }\n        return readCookie(this.cookies[name], options);\n    }\n    getAll(options = {}) {\n        if (!options.doNotUpdate) {\n            this.update();\n        }\n        const result = {};\n        for (let name in this.cookies) {\n            result[name] = readCookie(this.cookies[name], options);\n        }\n        return result;\n    }\n    set(name, value, options) {\n        if (options) {\n            options = Object.assign(Object.assign({}, this.defaultSetOptions), options);\n        }\n        else {\n            options = this.defaultSetOptions;\n        }\n        const stringValue = typeof value === 'string' ? value : JSON.stringify(value);\n        this.cookies = Object.assign(Object.assign({}, this.cookies), { [name]: stringValue });\n        if (this.HAS_DOCUMENT_COOKIE) {\n            document.cookie = cookieExports.serialize(name, stringValue, options);\n        }\n        this._emitChange({ name, value, options });\n    }\n    remove(name, options) {\n        const finalOptions = (options = Object.assign(Object.assign(Object.assign({}, this.defaultSetOptions), options), { expires: new Date(1970, 1, 1, 0, 0, 1), maxAge: 0 }));\n        this.cookies = Object.assign({}, this.cookies);\n        delete this.cookies[name];\n        if (this.HAS_DOCUMENT_COOKIE) {\n            document.cookie = cookieExports.serialize(name, '', finalOptions);\n        }\n        this._emitChange({ name, value: undefined, options });\n    }\n    addChangeListener(callback) {\n        this.changeListeners.push(callback);\n        if (this.HAS_DOCUMENT_COOKIE && this.changeListeners.length === 1) {\n            if (typeof window === 'object' && 'cookieStore' in window) {\n                window.cookieStore.addEventListener('change', this.update);\n            }\n            else {\n                this._startPolling();\n            }\n        }\n    }\n    removeChangeListener(callback) {\n        const idx = this.changeListeners.indexOf(callback);\n        if (idx >= 0) {\n            this.changeListeners.splice(idx, 1);\n        }\n        if (this.HAS_DOCUMENT_COOKIE && this.changeListeners.length === 0) {\n            if (typeof window === 'object' && 'cookieStore' in window) {\n                window.cookieStore.removeEventListener('change', this.update);\n            }\n            else {\n                this._stopPolling();\n            }\n        }\n    }\n}\n\nexport { Cookies as default };\n"], "mappings": ";;;AAAA,IAAI,SAAS,CAAC;AASd,IAAI;AAEJ,SAAS,gBAAiB;AACzB,MAAI,kBAAmB,QAAO;AAC9B,sBAAoB;AAOpB,SAAO,QAAQ;AACf,SAAO,YAAY;AAOnB,MAAI,aAAa,OAAO,UAAU;AAClC,MAAI,mBAAmB,OAAO,UAAU;AAcxC,MAAI,mBAAmB;AAYvB,MAAI,oBAAoB;AA0BxB,MAAI,oBAAoB;AAUxB,MAAI,kBAAkB;AActB,WAAS,MAAM,KAAK,KAAK;AACvB,QAAI,OAAO,QAAQ,UAAU;AAC3B,YAAM,IAAI,UAAU,+BAA+B;AAAA,IACrD;AAEA,QAAI,MAAM,CAAC;AACX,QAAI,MAAM,IAAI;AAEd,QAAI,MAAM,EAAG,QAAO;AAEpB,QAAI,MAAO,OAAO,IAAI,UAAW;AACjC,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,SAAS;AAEb,OAAG;AACD,cAAQ,IAAI,QAAQ,KAAK,KAAK;AAC9B,UAAI,UAAU,GAAI;AAElB,eAAS,IAAI,QAAQ,KAAK,KAAK;AAE/B,UAAI,WAAW,IAAI;AACjB,iBAAS;AAAA,MACX,WAAW,QAAQ,QAAQ;AAEzB,gBAAQ,IAAI,YAAY,KAAK,QAAQ,CAAC,IAAI;AAC1C;AAAA,MACF;AAEA,UAAI,cAAc,WAAW,KAAK,OAAO,KAAK;AAC9C,UAAI,YAAY,SAAS,KAAK,OAAO,WAAW;AAChD,UAAI,MAAM,IAAI,MAAM,aAAa,SAAS;AAG1C,UAAI,CAAC,iBAAiB,KAAK,KAAK,GAAG,GAAG;AACpC,YAAI,cAAc,WAAW,KAAK,QAAQ,GAAG,MAAM;AACnD,YAAI,YAAY,SAAS,KAAK,QAAQ,WAAW;AAEjD,YAAI,IAAI,WAAW,WAAW,MAAM,MAAgB,IAAI,WAAW,YAAY,CAAC,MAAM,IAAc;AAClG;AACA;AAAA,QACF;AAEA,YAAI,MAAM,IAAI,MAAM,aAAa,SAAS;AAC1C,YAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AAAA,MAC/B;AAEA,cAAQ,SAAS;AAAA,IACnB,SAAS,QAAQ;AAEjB,WAAO;AAAA,EACT;AAEA,WAAS,WAAW,KAAK,OAAO,KAAK;AACnC,OAAG;AACD,UAAI,OAAO,IAAI,WAAW,KAAK;AAC/B,UAAI,SAAS,MAAgB,SAAS,EAAe,QAAO;AAAA,IAC9D,SAAS,EAAE,QAAQ;AACnB,WAAO;AAAA,EACT;AAEA,WAAS,SAAS,KAAK,OAAO,KAAK;AACjC,WAAO,QAAQ,KAAK;AAClB,UAAI,OAAO,IAAI,WAAW,EAAE,KAAK;AACjC,UAAI,SAAS,MAAgB,SAAS,EAAe,QAAO,QAAQ;AAAA,IACtE;AACA,WAAO;AAAA,EACT;AAkBA,WAAS,UAAU,MAAM,KAAK,KAAK;AACjC,QAAI,MAAO,OAAO,IAAI,UAAW;AAEjC,QAAI,OAAO,QAAQ,YAAY;AAC7B,YAAM,IAAI,UAAU,0BAA0B;AAAA,IAChD;AAEA,QAAI,CAAC,iBAAiB,KAAK,IAAI,GAAG;AAChC,YAAM,IAAI,UAAU,0BAA0B;AAAA,IAChD;AAEA,QAAI,QAAQ,IAAI,GAAG;AAEnB,QAAI,CAAC,kBAAkB,KAAK,KAAK,GAAG;AAClC,YAAM,IAAI,UAAU,yBAAyB;AAAA,IAC/C;AAEA,QAAI,MAAM,OAAO,MAAM;AACvB,QAAI,CAAC,IAAK,QAAO;AAEjB,QAAI,QAAQ,IAAI,QAAQ;AACtB,UAAI,SAAS,KAAK,MAAM,IAAI,MAAM;AAElC,UAAI,CAAC,SAAS,MAAM,GAAG;AACrB,cAAM,IAAI,UAAU,0BAA0B;AAAA,MAChD;AAEA,aAAO,eAAe;AAAA,IACxB;AAEA,QAAI,IAAI,QAAQ;AACd,UAAI,CAAC,kBAAkB,KAAK,IAAI,MAAM,GAAG;AACvC,cAAM,IAAI,UAAU,0BAA0B;AAAA,MAChD;AAEA,aAAO,cAAc,IAAI;AAAA,IAC3B;AAEA,QAAI,IAAI,MAAM;AACZ,UAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,GAAG;AACnC,cAAM,IAAI,UAAU,wBAAwB;AAAA,MAC9C;AAEA,aAAO,YAAY,IAAI;AAAA,IACzB;AAEA,QAAI,IAAI,SAAS;AACf,UAAI,UAAU,IAAI;AAElB,UAAI,CAAC,OAAO,OAAO,KAAK,MAAM,QAAQ,QAAQ,CAAC,GAAG;AAChD,cAAM,IAAI,UAAU,2BAA2B;AAAA,MACjD;AAEA,aAAO,eAAe,QAAQ,YAAY;AAAA,IAC5C;AAEA,QAAI,IAAI,UAAU;AAChB,aAAO;AAAA,IACT;AAEA,QAAI,IAAI,QAAQ;AACd,aAAO;AAAA,IACT;AAEA,QAAI,IAAI,aAAa;AACnB,aAAO;AAAA,IACT;AAEA,QAAI,IAAI,UAAU;AAChB,UAAI,WAAW,OAAO,IAAI,aAAa,WACnC,IAAI,SAAS,YAAY,IAAI,IAAI;AAErC,cAAQ,UAAU;AAAA,QAChB,KAAK;AACH,iBAAO;AACP;AAAA,QACF,KAAK;AACH,iBAAO;AACP;AAAA,QACF,KAAK;AACH,iBAAO;AACP;AAAA,QACF;AACE,gBAAM,IAAI,UAAU,4BAA4B;AAAA,MACpD;AAAA,IACF;AAEA,QAAI,IAAI,UAAU;AAChB,UAAI,WAAW,OAAO,IAAI,aAAa,WACnC,IAAI,SAAS,YAAY,IAAI,IAAI;AAErC,cAAQ,UAAU;AAAA,QAChB,KAAK;AACH,iBAAO;AACP;AAAA,QACF,KAAK;AACH,iBAAO;AACP;AAAA,QACF,KAAK;AACH,iBAAO;AACP;AAAA,QACF,KAAK;AACH,iBAAO;AACP;AAAA,QACF;AACE,gBAAM,IAAI,UAAU,4BAA4B;AAAA,MACpD;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AASA,WAAS,OAAQ,KAAK;AACpB,WAAO,IAAI,QAAQ,GAAG,MAAM,KACxB,mBAAmB,GAAG,IACtB;AAAA,EACN;AASA,WAAS,OAAQ,KAAK;AACpB,WAAO,WAAW,KAAK,GAAG,MAAM;AAAA,EAClC;AAUA,WAAS,UAAU,KAAKA,SAAQ;AAC9B,QAAI;AACF,aAAOA,QAAO,GAAG;AAAA,IACnB,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACR;AAEA,IAAI,gBAAgB,cAAc;AAElC,SAAS,oBAAoB;AACzB,QAAM,eAAe,OAAO,WAAW,cACjC,SACA,OAAO;AACb,MAAI,OAAO,iBAAiB,WAAW;AACnC,WAAO;AAAA,EACX;AAEA,SAAO,OAAO,aAAa,YAAY,OAAO,SAAS,WAAW;AACtE;AACA,SAAS,aAAa,SAAS;AAC3B,MAAI,OAAO,YAAY,UAAU;AAC7B,WAAO,cAAc,MAAM,OAAO;AAAA,EACtC,WACS,OAAO,YAAY,YAAY,YAAY,MAAM;AACtD,WAAO;AAAA,EACX,OACK;AACD,WAAO,CAAC;AAAA,EACZ;AACJ;AACA,SAAS,WAAW,OAAO,UAAU,CAAC,GAAG;AACrC,QAAM,aAAa,mBAAmB,KAAK;AAC3C,MAAI,CAAC,QAAQ,YAAY;AACrB,QAAI;AACA,aAAO,KAAK,MAAM,UAAU;AAAA,IAChC,SACO,GAAG;AAAA,IAEV;AAAA,EACJ;AAGA,SAAO;AACX;AACA,SAAS,mBAAmB,OAAO;AAE/B,MAAI,SAAS,MAAM,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM,KAAK;AAC/C,WAAO,MAAM,OAAO,CAAC;AAAA,EACzB;AACA,SAAO;AACX;AAEA,IAAM,UAAN,MAAc;AAAA,EACV,YAAY,SAAS,oBAAoB,CAAC,GAAG;AACzC,SAAK,kBAAkB,CAAC;AACxB,SAAK,sBAAsB;AAC3B,SAAK,SAAS,MAAM;AAChB,UAAI,CAAC,KAAK,qBAAqB;AAC3B;AAAA,MACJ;AACA,YAAM,kBAAkB,KAAK;AAC7B,WAAK,UAAU,cAAc,MAAM,SAAS,MAAM;AAClD,WAAK,cAAc,eAAe;AAAA,IACtC;AACA,UAAM,aAAa,OAAO,aAAa,cAAc,KAAK,SAAS;AACnE,SAAK,UAAU,aAAa,WAAW,UAAU;AACjD,SAAK,oBAAoB;AACzB,SAAK,sBAAsB,kBAAkB;AAAA,EACjD;AAAA,EACA,YAAY,QAAQ;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,gBAAgB,QAAQ,EAAE,GAAG;AAClD,WAAK,gBAAgB,CAAC,EAAE,MAAM;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,cAAc,iBAAiB;AAC3B,UAAM,QAAQ,IAAI,IAAI,OAAO,KAAK,eAAe,EAAE,OAAO,OAAO,KAAK,KAAK,OAAO,CAAC,CAAC;AACpF,UAAM,QAAQ,CAAC,SAAS;AACpB,UAAI,gBAAgB,IAAI,MAAM,KAAK,QAAQ,IAAI,GAAG;AAC9C,aAAK,YAAY;AAAA,UACb;AAAA,UACA,OAAO,WAAW,KAAK,QAAQ,IAAI,CAAC;AAAA,QACxC,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,gBAAgB;AACZ,SAAK,kBAAkB,YAAY,KAAK,QAAQ,GAAG;AAAA,EACvD;AAAA,EACA,eAAe;AACX,QAAI,KAAK,iBAAiB;AACtB,oBAAc,KAAK,eAAe;AAAA,IACtC;AAAA,EACJ;AAAA,EACA,IAAI,MAAM,UAAU,CAAC,GAAG;AACpB,QAAI,CAAC,QAAQ,aAAa;AACtB,WAAK,OAAO;AAAA,IAChB;AACA,WAAO,WAAW,KAAK,QAAQ,IAAI,GAAG,OAAO;AAAA,EACjD;AAAA,EACA,OAAO,UAAU,CAAC,GAAG;AACjB,QAAI,CAAC,QAAQ,aAAa;AACtB,WAAK,OAAO;AAAA,IAChB;AACA,UAAM,SAAS,CAAC;AAChB,aAAS,QAAQ,KAAK,SAAS;AAC3B,aAAO,IAAI,IAAI,WAAW,KAAK,QAAQ,IAAI,GAAG,OAAO;AAAA,IACzD;AACA,WAAO;AAAA,EACX;AAAA,EACA,IAAI,MAAM,OAAO,SAAS;AACtB,QAAI,SAAS;AACT,gBAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,iBAAiB,GAAG,OAAO;AAAA,IAC9E,OACK;AACD,gBAAU,KAAK;AAAA,IACnB;AACA,UAAM,cAAc,OAAO,UAAU,WAAW,QAAQ,KAAK,UAAU,KAAK;AAC5E,SAAK,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO,GAAG,EAAE,CAAC,IAAI,GAAG,YAAY,CAAC;AACrF,QAAI,KAAK,qBAAqB;AAC1B,eAAS,SAAS,cAAc,UAAU,MAAM,aAAa,OAAO;AAAA,IACxE;AACA,SAAK,YAAY,EAAE,MAAM,OAAO,QAAQ,CAAC;AAAA,EAC7C;AAAA,EACA,OAAO,MAAM,SAAS;AAClB,UAAM,eAAgB,UAAU,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,iBAAiB,GAAG,OAAO,GAAG,EAAE,SAAS,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,QAAQ,EAAE,CAAC;AACtK,SAAK,UAAU,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO;AAC7C,WAAO,KAAK,QAAQ,IAAI;AACxB,QAAI,KAAK,qBAAqB;AAC1B,eAAS,SAAS,cAAc,UAAU,MAAM,IAAI,YAAY;AAAA,IACpE;AACA,SAAK,YAAY,EAAE,MAAM,OAAO,QAAW,QAAQ,CAAC;AAAA,EACxD;AAAA,EACA,kBAAkB,UAAU;AACxB,SAAK,gBAAgB,KAAK,QAAQ;AAClC,QAAI,KAAK,uBAAuB,KAAK,gBAAgB,WAAW,GAAG;AAC/D,UAAI,OAAO,WAAW,YAAY,iBAAiB,QAAQ;AACvD,eAAO,YAAY,iBAAiB,UAAU,KAAK,MAAM;AAAA,MAC7D,OACK;AACD,aAAK,cAAc;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,qBAAqB,UAAU;AAC3B,UAAM,MAAM,KAAK,gBAAgB,QAAQ,QAAQ;AACjD,QAAI,OAAO,GAAG;AACV,WAAK,gBAAgB,OAAO,KAAK,CAAC;AAAA,IACtC;AACA,QAAI,KAAK,uBAAuB,KAAK,gBAAgB,WAAW,GAAG;AAC/D,UAAI,OAAO,WAAW,YAAY,iBAAiB,QAAQ;AACvD,eAAO,YAAY,oBAAoB,UAAU,KAAK,MAAM;AAAA,MAChE,OACK;AACD,aAAK,aAAa;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AACJ;", "names": ["decode"]}