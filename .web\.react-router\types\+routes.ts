// Generated by React Router

import "react-router"

declare module "react-router" {
  interface Register {
    pages: Pages
    routeFiles: RouteFiles
  }
}

type Pages = {
  "/404": {
    params: {};
  };
  "/": {
    params: {};
  };
  "/assessments": {
    params: {};
  };
  "/dashboard": {
    params: {};
  };
  "/patients": {
    params: {};
  };
  "/health": {
    params: {};
  };
  "/login": {
    params: {};
  };
  "/*": {
    params: {
      "*": string;
    };
  };
};

type RouteFiles = {
  "routes/[404]._index.jsx": {
    id: "404";
    page: "/404";
  } | {
    id: "routes/[404]._index";
    page: "/*";
  };
  "root.jsx": {
    id: "root";
    page: "/404" | "/" | "/assessments" | "/dashboard" | "/patients" | "/health" | "/login" | "/*";
  };
  "routes/[assessments]._index.jsx": {
    id: "routes/[assessments]._index";
    page: "/assessments";
  };
  "routes/[dashboard]._index.jsx": {
    id: "routes/[dashboard]._index";
    page: "/dashboard";
  };
  "routes/[patients]._index.jsx": {
    id: "routes/[patients]._index";
    page: "/patients";
  };
  "routes/[health]._index.jsx": {
    id: "routes/[health]._index";
    page: "/health";
  };
  "routes/[login]._index.jsx": {
    id: "routes/[login]._index";
    page: "/login";
  };
  "routes/_index.jsx": {
    id: "routes/_index";
    page: "/";
  };
};