// Generated by React Router

import "react-router"

declare module "react-router" {
  interface Register {
    pages: Pages
    routeFiles: RouteFiles
  }
}

type Pages = {
  "/404": {
    params: {};
  };
  "/": {
    params: {};
  };
  "/*": {
    params: {
      "*": string;
    };
  };
};

type RouteFiles = {
  "routes/[404]._index.jsx": {
    id: "404";
    page: "/404";
  } | {
    id: "routes/[404]._index";
    page: "/*";
  };
  "root.jsx": {
    id: "root";
    page: "/404" | "/" | "/*";
  };
  "routes/_index.jsx": {
    id: "routes/_index";
    page: "/";
  };
};