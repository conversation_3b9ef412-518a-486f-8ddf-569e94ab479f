!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@floating-ui/dom"),require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","@floating-ui/dom","react","react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIReactDOM={},e.FloatingUIDOM,e.<PERSON>act,e.ReactDOM)}(this,(function(e,t,n,r){"use strict";function o(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var i=o(n),u=o(r),f="undefined"!=typeof document?n.useLayoutEffect:function(){};function c(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!=r--;)if(!c(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!c(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function a(e){if("undefined"==typeof window)return 1;return(e.ownerDocument.defaultView||window).devicePixelRatio||1}function s(e,t){const n=a(e);return Math.round(t*n)/n}function l(e){const t=i.useRef(e);return f((()=>{t.current=e})),t}const p=e=>({name:"arrow",options:e,fn(n){const{element:r,padding:o}="function"==typeof e?e(n):e;return r&&(i=r,{}.hasOwnProperty.call(i,"current"))?null!=r.current?t.arrow({element:r.current,padding:o}).fn(n):{}:r?t.arrow({element:r,padding:o}).fn(n):{};var i}});Object.defineProperty(e,"autoUpdate",{enumerable:!0,get:function(){return t.autoUpdate}}),Object.defineProperty(e,"computePosition",{enumerable:!0,get:function(){return t.computePosition}}),Object.defineProperty(e,"detectOverflow",{enumerable:!0,get:function(){return t.detectOverflow}}),Object.defineProperty(e,"getOverflowAncestors",{enumerable:!0,get:function(){return t.getOverflowAncestors}}),Object.defineProperty(e,"platform",{enumerable:!0,get:function(){return t.platform}}),e.arrow=(e,t)=>({...p(e),options:[e,t]}),e.autoPlacement=(e,n)=>({...t.autoPlacement(e),options:[e,n]}),e.flip=(e,n)=>({...t.flip(e),options:[e,n]}),e.hide=(e,n)=>({...t.hide(e),options:[e,n]}),e.inline=(e,n)=>({...t.inline(e),options:[e,n]}),e.limitShift=(e,n)=>({...t.limitShift(e),options:[e,n]}),e.offset=(e,n)=>({...t.offset(e),options:[e,n]}),e.shift=(e,n)=>({...t.shift(e),options:[e,n]}),e.size=(e,n)=>({...t.size(e),options:[e,n]}),e.useFloating=function(e){void 0===e&&(e={});const{placement:n="bottom",strategy:r="absolute",middleware:o=[],platform:p,elements:{reference:d,floating:m}={},transform:g=!0,whileElementsMounted:y,open:b}=e,[w,O]=i.useState({x:0,y:0,strategy:r,placement:n,middlewareData:{},isPositioned:!1}),[h,P]=i.useState(o);c(h,o)||P(o);const[j,v]=i.useState(null),[R,S]=i.useState(null),x=i.useCallback((e=>{e!==A.current&&(A.current=e,v(e))}),[]),M=i.useCallback((e=>{e!==C.current&&(C.current=e,S(e))}),[]),k=d||j,D=m||R,A=i.useRef(null),C=i.useRef(null),F=i.useRef(w),U=null!=y,q=l(y),z=l(p),E=l(b),I=i.useCallback((()=>{if(!A.current||!C.current)return;const e={placement:n,strategy:r,middleware:h};z.current&&(e.platform=z.current),t.computePosition(A.current,C.current,e).then((e=>{const t={...e,isPositioned:!1!==E.current};T.current&&!c(F.current,t)&&(F.current=t,u.flushSync((()=>{O(t)})))}))}),[h,n,r,z,E]);f((()=>{!1===b&&F.current.isPositioned&&(F.current.isPositioned=!1,O((e=>({...e,isPositioned:!1}))))}),[b]);const T=i.useRef(!1);f((()=>(T.current=!0,()=>{T.current=!1})),[]),f((()=>{if(k&&(A.current=k),D&&(C.current=D),k&&D){if(q.current)return q.current(k,D,I);I()}}),[k,D,I,q,U]);const $=i.useMemo((()=>({reference:A,floating:C,setReference:x,setFloating:M})),[x,M]),L=i.useMemo((()=>({reference:k,floating:D})),[k,D]),V=i.useMemo((()=>{const e={position:r,left:0,top:0};if(!L.floating)return e;const t=s(L.floating,w.x),n=s(L.floating,w.y);return g?{...e,transform:"translate("+t+"px, "+n+"px)",...a(L.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}}),[r,g,L.floating,w.x,w.y]);return i.useMemo((()=>({...w,update:I,refs:$,elements:L,floatingStyles:V})),[w,I,$,L,V])}}));
