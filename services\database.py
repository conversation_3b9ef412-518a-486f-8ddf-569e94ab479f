"""
Psychiatry EMR - Database Connection Management
Database connection with connection pooling and performance optimization.
"""

from sqlmodel import Session, create_engine, SQLModel
from contextlib import contextmanager
from config.settings import get_settings
import logging
import os
from pathlib import Path

logger = logging.getLogger(__name__)

# Global engine instance - will be initialized when needed
_engine = None

def get_engine():
    """Get database engine with lazy initialization"""
    global _engine
    if _engine is None:
        settings = get_settings()
        
        # Database connection with connection pooling
        _engine = create_engine(
            settings.database_url,
            echo=settings.debug_mode,
            pool_size=settings.db_pool_size,
            max_overflow=settings.db_max_overflow,
            pool_pre_ping=True,  # Verify connections before use
            pool_recycle=3600,   # Recycle connections every hour
        )
        logger.info("Database engine initialized")
    
    return _engine

@contextmanager
def get_db_session():
    """Context manager for database sessions with proper cleanup"""
    engine = get_engine()
    session = Session(engine)
    try:
        yield session
    except Exception as e:
        session.rollback()
        logger.error(f"Database session error: {e}")
        raise
    finally:
        session.close()

# Additional database indexes for performance
DB_INDEXES = """
-- Performance indexes for common queries
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);

CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id) WHERE is_active = true;

-- Partial index for active patients only (more efficient)
CREATE INDEX IF NOT EXISTS idx_patient_active_only ON patient(id) WHERE is_active = true;
"""

def initialize_database():
    """Initialize database with tables and security setup"""
    engine = get_engine()

    try:
        # Create all tables
        SQLModel.metadata.create_all(engine)
        logger.info("Database tables created successfully")

        # Apply security setup
        apply_sql_script("setup_security.sql")

        # Apply performance indexes
        apply_sql_script("performance_indexes.sql")

        logger.info("Database initialization completed")

    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise

def apply_sql_script(script_name: str):
    """Apply SQL script from sql/ directory"""
    script_path = Path("sql") / script_name

    if not script_path.exists():
        logger.warning(f"SQL script not found: {script_path}")
        return

    try:
        with open(script_path, 'r') as f:
            sql_content = f.read()

        engine = get_engine()

        # Skip PostgreSQL-specific scripts for SQLite
        if "sqlite" in str(engine.url) and ("pgcrypto" in sql_content or "CREATE EXTENSION" in sql_content):
            logger.info(f"Skipping PostgreSQL-specific script {script_name} for SQLite database")
            return

        with engine.connect() as connection:
            # Execute SQL script in chunks (split by semicolon)
            statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]

            for statement in statements:
                if statement and not statement.startswith('--'):
                    try:
                        from sqlalchemy import text
                        connection.execute(text(statement))
                    except Exception as stmt_error:
                        logger.warning(f"Failed to execute statement: {statement[:50]}... Error: {stmt_error}")

            connection.commit()

        logger.info(f"Applied SQL script: {script_name}")

    except Exception as e:
        logger.error(f"Failed to apply SQL script {script_name}: {e}")
        # Don't raise for SQL script errors in development
        if "sqlite" not in str(get_engine().url):
            raise

def set_user_context(session: Session, user_id: int):
    """Set user context for Row Level Security"""
    try:
        session.exec(f"SELECT set_current_user({user_id})")
        logger.debug(f"Set user context: {user_id}")
    except Exception as e:
        logger.error(f"Failed to set user context: {e}")
        raise

def clear_user_context(session: Session):
    """Clear user context for Row Level Security"""
    try:
        session.exec("SELECT clear_current_user()")
        logger.debug("Cleared user context")
    except Exception as e:
        logger.error(f"Failed to clear user context: {e}")

@contextmanager
def get_db_session_with_user(user_id: int):
    """Context manager for database sessions with user context for RLS"""
    engine = get_engine()
    session = Session(engine)
    try:
        set_user_context(session, user_id)
        yield session
    except Exception as e:
        session.rollback()
        logger.error(f"Database session error: {e}")
        raise
    finally:
        clear_user_context(session)
        session.close()

def check_database_health():
    """Check database connection and basic health"""
    try:
        engine = get_engine()
        with engine.connect() as connection:
            result = connection.execute("SELECT 1")
            return result.scalar() == 1
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False
