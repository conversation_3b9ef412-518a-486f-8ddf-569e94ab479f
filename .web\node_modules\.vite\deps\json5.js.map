{"version": 3, "sources": ["../../json5/dist/index.mjs"], "sourcesContent": ["// This is a generated file. Do not edit.\nvar Space_Separator = /[\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nvar ID_Start = /[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u08A0-\\u08B4\\u08B6-\\u08BD\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312E\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FEA\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7AE\\uA7B0-\\uA7B7\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF4A\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE33\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2]|\\uD804[\\uDC03-\\uDC37\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDF00-\\uDF19]|\\uD806[\\uDCA0-\\uDCDF\\uDCFF\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE83\\uDE86-\\uDE89\\uDEC0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|[\\uD80C\\uD81C-\\uD820\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDF00-\\uDF44\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1]|\\uD821[\\uDC00-\\uDFEC]|\\uD822[\\uDC00-\\uDEF2]|\\uD82C[\\uDC00-\\uDD1E\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDED6\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF34\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]/;\nvar ID_Continue = /[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0300-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u0483-\\u0487\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0610-\\u061A\\u0620-\\u0669\\u066E-\\u06D3\\u06D5-\\u06DC\\u06DF-\\u06E8\\u06EA-\\u06FC\\u06FF\\u0710-\\u074A\\u074D-\\u07B1\\u07C0-\\u07F5\\u07FA\\u0800-\\u082D\\u0840-\\u085B\\u0860-\\u086A\\u08A0-\\u08B4\\u08B6-\\u08BD\\u08D4-\\u08E1\\u08E3-\\u0963\\u0966-\\u096F\\u0971-\\u0983\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BC-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CE\\u09D7\\u09DC\\u09DD\\u09DF-\\u09E3\\u09E6-\\u09F1\\u09FC\\u0A01-\\u0A03\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A59-\\u0A5C\\u0A5E\\u0A66-\\u0A75\\u0A81-\\u0A83\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABC-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AD0\\u0AE0-\\u0AE3\\u0AE6-\\u0AEF\\u0AF9-\\u0AFF\\u0B01-\\u0B03\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3C-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B56\\u0B57\\u0B5C\\u0B5D\\u0B5F-\\u0B63\\u0B66-\\u0B6F\\u0B71\\u0B82\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD0\\u0BD7\\u0BE6-\\u0BEF\\u0C00-\\u0C03\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C58-\\u0C5A\\u0C60-\\u0C63\\u0C66-\\u0C6F\\u0C80-\\u0C83\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBC-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CDE\\u0CE0-\\u0CE3\\u0CE6-\\u0CEF\\u0CF1\\u0CF2\\u0D00-\\u0D03\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4E\\u0D54-\\u0D57\\u0D5F-\\u0D63\\u0D66-\\u0D6F\\u0D7A-\\u0D7F\\u0D82\\u0D83\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DE6-\\u0DEF\\u0DF2\\u0DF3\\u0E01-\\u0E3A\\u0E40-\\u0E4E\\u0E50-\\u0E59\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB9\\u0EBB-\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EC8-\\u0ECD\\u0ED0-\\u0ED9\\u0EDC-\\u0EDF\\u0F00\\u0F18\\u0F19\\u0F20-\\u0F29\\u0F35\\u0F37\\u0F39\\u0F3E-\\u0F47\\u0F49-\\u0F6C\\u0F71-\\u0F84\\u0F86-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u1000-\\u1049\\u1050-\\u109D\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u135D-\\u135F\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1714\\u1720-\\u1734\\u1740-\\u1753\\u1760-\\u176C\\u176E-\\u1770\\u1772\\u1773\\u1780-\\u17D3\\u17D7\\u17DC\\u17DD\\u17E0-\\u17E9\\u180B-\\u180D\\u1810-\\u1819\\u1820-\\u1877\\u1880-\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1920-\\u192B\\u1930-\\u193B\\u1946-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u19D0-\\u19D9\\u1A00-\\u1A1B\\u1A20-\\u1A5E\\u1A60-\\u1A7C\\u1A7F-\\u1A89\\u1A90-\\u1A99\\u1AA7\\u1AB0-\\u1ABD\\u1B00-\\u1B4B\\u1B50-\\u1B59\\u1B6B-\\u1B73\\u1B80-\\u1BF3\\u1C00-\\u1C37\\u1C40-\\u1C49\\u1C4D-\\u1C7D\\u1C80-\\u1C88\\u1CD0-\\u1CD2\\u1CD4-\\u1CF9\\u1D00-\\u1DF9\\u1DFB-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u203F\\u2040\\u2054\\u2071\\u207F\\u2090-\\u209C\\u20D0-\\u20DC\\u20E1\\u20E5-\\u20F0\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D7F-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2DE0-\\u2DFF\\u2E2F\\u3005-\\u3007\\u3021-\\u302F\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u3099\\u309A\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312E\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FEA\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66F\\uA674-\\uA67D\\uA67F-\\uA6F1\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7AE\\uA7B0-\\uA7B7\\uA7F7-\\uA827\\uA840-\\uA873\\uA880-\\uA8C5\\uA8D0-\\uA8D9\\uA8E0-\\uA8F7\\uA8FB\\uA8FD\\uA900-\\uA92D\\uA930-\\uA953\\uA960-\\uA97C\\uA980-\\uA9C0\\uA9CF-\\uA9D9\\uA9E0-\\uA9FE\\uAA00-\\uAA36\\uAA40-\\uAA4D\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A-\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEF\\uAAF2-\\uAAF6\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABEA\\uABEC\\uABED\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE00-\\uFE0F\\uFE20-\\uFE2F\\uFE33\\uFE34\\uFE4D-\\uFE4F\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF3F\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDDFD\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDEE0\\uDF00-\\uDF1F\\uDF2D-\\uDF4A\\uDF50-\\uDF7A\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCA0-\\uDCA9\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00-\\uDE03\\uDE05\\uDE06\\uDE0C-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE33\\uDE38-\\uDE3A\\uDE3F\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE6\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2]|\\uD804[\\uDC00-\\uDC46\\uDC66-\\uDC6F\\uDC7F-\\uDCBA\\uDCD0-\\uDCE8\\uDCF0-\\uDCF9\\uDD00-\\uDD34\\uDD36-\\uDD3F\\uDD50-\\uDD73\\uDD76\\uDD80-\\uDDC4\\uDDCA-\\uDDCC\\uDDD0-\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE37\\uDE3E\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEEA\\uDEF0-\\uDEF9\\uDF00-\\uDF03\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3C-\\uDF44\\uDF47\\uDF48\\uDF4B-\\uDF4D\\uDF50\\uDF57\\uDF5D-\\uDF63\\uDF66-\\uDF6C\\uDF70-\\uDF74]|\\uD805[\\uDC00-\\uDC4A\\uDC50-\\uDC59\\uDC80-\\uDCC5\\uDCC7\\uDCD0-\\uDCD9\\uDD80-\\uDDB5\\uDDB8-\\uDDC0\\uDDD8-\\uDDDD\\uDE00-\\uDE40\\uDE44\\uDE50-\\uDE59\\uDE80-\\uDEB7\\uDEC0-\\uDEC9\\uDF00-\\uDF19\\uDF1D-\\uDF2B\\uDF30-\\uDF39]|\\uD806[\\uDCA0-\\uDCE9\\uDCFF\\uDE00-\\uDE3E\\uDE47\\uDE50-\\uDE83\\uDE86-\\uDE99\\uDEC0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC36\\uDC38-\\uDC40\\uDC50-\\uDC59\\uDC72-\\uDC8F\\uDC92-\\uDCA7\\uDCA9-\\uDCB6\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD36\\uDD3A\\uDD3C\\uDD3D\\uDD3F-\\uDD47\\uDD50-\\uDD59]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|[\\uD80C\\uD81C-\\uD820\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE60-\\uDE69\\uDED0-\\uDEED\\uDEF0-\\uDEF4\\uDF00-\\uDF36\\uDF40-\\uDF43\\uDF50-\\uDF59\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDF00-\\uDF44\\uDF50-\\uDF7E\\uDF8F-\\uDF9F\\uDFE0\\uDFE1]|\\uD821[\\uDC00-\\uDFEC]|\\uD822[\\uDC00-\\uDEF2]|\\uD82C[\\uDC00-\\uDD1E\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99\\uDC9D\\uDC9E]|\\uD834[\\uDD65-\\uDD69\\uDD6D-\\uDD72\\uDD7B-\\uDD82\\uDD85-\\uDD8B\\uDDAA-\\uDDAD\\uDE42-\\uDE44]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB\\uDFCE-\\uDFFF]|\\uD836[\\uDE00-\\uDE36\\uDE3B-\\uDE6C\\uDE75\\uDE84\\uDE9B-\\uDE9F\\uDEA1-\\uDEAF]|\\uD838[\\uDC00-\\uDC06\\uDC08-\\uDC18\\uDC1B-\\uDC21\\uDC23\\uDC24\\uDC26-\\uDC2A]|\\uD83A[\\uDC00-\\uDCC4\\uDCD0-\\uDCD6\\uDD00-\\uDD4A\\uDD50-\\uDD59]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDED6\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF34\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]|\\uDB40[\\uDD00-\\uDDEF]/;\n\nvar unicode = {\n\tSpace_Separator: Space_Separator,\n\tID_Start: ID_Start,\n\tID_Continue: ID_Continue\n};\n\nvar util = {\n    isSpaceSeparator (c) {\n        return typeof c === 'string' && unicode.Space_Separator.test(c)\n    },\n\n    isIdStartChar (c) {\n        return typeof c === 'string' && (\n            (c >= 'a' && c <= 'z') ||\n        (c >= 'A' && c <= 'Z') ||\n        (c === '$') || (c === '_') ||\n        unicode.ID_Start.test(c)\n        )\n    },\n\n    isIdContinueChar (c) {\n        return typeof c === 'string' && (\n            (c >= 'a' && c <= 'z') ||\n        (c >= 'A' && c <= 'Z') ||\n        (c >= '0' && c <= '9') ||\n        (c === '$') || (c === '_') ||\n        (c === '\\u200C') || (c === '\\u200D') ||\n        unicode.ID_Continue.test(c)\n        )\n    },\n\n    isDigit (c) {\n        return typeof c === 'string' && /[0-9]/.test(c)\n    },\n\n    isHexDigit (c) {\n        return typeof c === 'string' && /[0-9A-Fa-f]/.test(c)\n    },\n};\n\nlet source;\nlet parseState;\nlet stack;\nlet pos;\nlet line;\nlet column;\nlet token;\nlet key;\nlet root;\n\nvar parse = function parse (text, reviver) {\n    source = String(text);\n    parseState = 'start';\n    stack = [];\n    pos = 0;\n    line = 1;\n    column = 0;\n    token = undefined;\n    key = undefined;\n    root = undefined;\n\n    do {\n        token = lex();\n\n        // This code is unreachable.\n        // if (!parseStates[parseState]) {\n        //     throw invalidParseState()\n        // }\n\n        parseStates[parseState]();\n    } while (token.type !== 'eof')\n\n    if (typeof reviver === 'function') {\n        return internalize({'': root}, '', reviver)\n    }\n\n    return root\n};\n\nfunction internalize (holder, name, reviver) {\n    const value = holder[name];\n    if (value != null && typeof value === 'object') {\n        if (Array.isArray(value)) {\n            for (let i = 0; i < value.length; i++) {\n                const key = String(i);\n                const replacement = internalize(value, key, reviver);\n                if (replacement === undefined) {\n                    delete value[key];\n                } else {\n                    Object.defineProperty(value, key, {\n                        value: replacement,\n                        writable: true,\n                        enumerable: true,\n                        configurable: true,\n                    });\n                }\n            }\n        } else {\n            for (const key in value) {\n                const replacement = internalize(value, key, reviver);\n                if (replacement === undefined) {\n                    delete value[key];\n                } else {\n                    Object.defineProperty(value, key, {\n                        value: replacement,\n                        writable: true,\n                        enumerable: true,\n                        configurable: true,\n                    });\n                }\n            }\n        }\n    }\n\n    return reviver.call(holder, name, value)\n}\n\nlet lexState;\nlet buffer;\nlet doubleQuote;\nlet sign;\nlet c;\n\nfunction lex () {\n    lexState = 'default';\n    buffer = '';\n    doubleQuote = false;\n    sign = 1;\n\n    for (;;) {\n        c = peek();\n\n        // This code is unreachable.\n        // if (!lexStates[lexState]) {\n        //     throw invalidLexState(lexState)\n        // }\n\n        const token = lexStates[lexState]();\n        if (token) {\n            return token\n        }\n    }\n}\n\nfunction peek () {\n    if (source[pos]) {\n        return String.fromCodePoint(source.codePointAt(pos))\n    }\n}\n\nfunction read () {\n    const c = peek();\n\n    if (c === '\\n') {\n        line++;\n        column = 0;\n    } else if (c) {\n        column += c.length;\n    } else {\n        column++;\n    }\n\n    if (c) {\n        pos += c.length;\n    }\n\n    return c\n}\n\nconst lexStates = {\n    default () {\n        switch (c) {\n        case '\\t':\n        case '\\v':\n        case '\\f':\n        case ' ':\n        case '\\u00A0':\n        case '\\uFEFF':\n        case '\\n':\n        case '\\r':\n        case '\\u2028':\n        case '\\u2029':\n            read();\n            return\n\n        case '/':\n            read();\n            lexState = 'comment';\n            return\n\n        case undefined:\n            read();\n            return newToken('eof')\n        }\n\n        if (util.isSpaceSeparator(c)) {\n            read();\n            return\n        }\n\n        // This code is unreachable.\n        // if (!lexStates[parseState]) {\n        //     throw invalidLexState(parseState)\n        // }\n\n        return lexStates[parseState]()\n    },\n\n    comment () {\n        switch (c) {\n        case '*':\n            read();\n            lexState = 'multiLineComment';\n            return\n\n        case '/':\n            read();\n            lexState = 'singleLineComment';\n            return\n        }\n\n        throw invalidChar(read())\n    },\n\n    multiLineComment () {\n        switch (c) {\n        case '*':\n            read();\n            lexState = 'multiLineCommentAsterisk';\n            return\n\n        case undefined:\n            throw invalidChar(read())\n        }\n\n        read();\n    },\n\n    multiLineCommentAsterisk () {\n        switch (c) {\n        case '*':\n            read();\n            return\n\n        case '/':\n            read();\n            lexState = 'default';\n            return\n\n        case undefined:\n            throw invalidChar(read())\n        }\n\n        read();\n        lexState = 'multiLineComment';\n    },\n\n    singleLineComment () {\n        switch (c) {\n        case '\\n':\n        case '\\r':\n        case '\\u2028':\n        case '\\u2029':\n            read();\n            lexState = 'default';\n            return\n\n        case undefined:\n            read();\n            return newToken('eof')\n        }\n\n        read();\n    },\n\n    value () {\n        switch (c) {\n        case '{':\n        case '[':\n            return newToken('punctuator', read())\n\n        case 'n':\n            read();\n            literal('ull');\n            return newToken('null', null)\n\n        case 't':\n            read();\n            literal('rue');\n            return newToken('boolean', true)\n\n        case 'f':\n            read();\n            literal('alse');\n            return newToken('boolean', false)\n\n        case '-':\n        case '+':\n            if (read() === '-') {\n                sign = -1;\n            }\n\n            lexState = 'sign';\n            return\n\n        case '.':\n            buffer = read();\n            lexState = 'decimalPointLeading';\n            return\n\n        case '0':\n            buffer = read();\n            lexState = 'zero';\n            return\n\n        case '1':\n        case '2':\n        case '3':\n        case '4':\n        case '5':\n        case '6':\n        case '7':\n        case '8':\n        case '9':\n            buffer = read();\n            lexState = 'decimalInteger';\n            return\n\n        case 'I':\n            read();\n            literal('nfinity');\n            return newToken('numeric', Infinity)\n\n        case 'N':\n            read();\n            literal('aN');\n            return newToken('numeric', NaN)\n\n        case '\"':\n        case \"'\":\n            doubleQuote = (read() === '\"');\n            buffer = '';\n            lexState = 'string';\n            return\n        }\n\n        throw invalidChar(read())\n    },\n\n    identifierNameStartEscape () {\n        if (c !== 'u') {\n            throw invalidChar(read())\n        }\n\n        read();\n        const u = unicodeEscape();\n        switch (u) {\n        case '$':\n        case '_':\n            break\n\n        default:\n            if (!util.isIdStartChar(u)) {\n                throw invalidIdentifier()\n            }\n\n            break\n        }\n\n        buffer += u;\n        lexState = 'identifierName';\n    },\n\n    identifierName () {\n        switch (c) {\n        case '$':\n        case '_':\n        case '\\u200C':\n        case '\\u200D':\n            buffer += read();\n            return\n\n        case '\\\\':\n            read();\n            lexState = 'identifierNameEscape';\n            return\n        }\n\n        if (util.isIdContinueChar(c)) {\n            buffer += read();\n            return\n        }\n\n        return newToken('identifier', buffer)\n    },\n\n    identifierNameEscape () {\n        if (c !== 'u') {\n            throw invalidChar(read())\n        }\n\n        read();\n        const u = unicodeEscape();\n        switch (u) {\n        case '$':\n        case '_':\n        case '\\u200C':\n        case '\\u200D':\n            break\n\n        default:\n            if (!util.isIdContinueChar(u)) {\n                throw invalidIdentifier()\n            }\n\n            break\n        }\n\n        buffer += u;\n        lexState = 'identifierName';\n    },\n\n    sign () {\n        switch (c) {\n        case '.':\n            buffer = read();\n            lexState = 'decimalPointLeading';\n            return\n\n        case '0':\n            buffer = read();\n            lexState = 'zero';\n            return\n\n        case '1':\n        case '2':\n        case '3':\n        case '4':\n        case '5':\n        case '6':\n        case '7':\n        case '8':\n        case '9':\n            buffer = read();\n            lexState = 'decimalInteger';\n            return\n\n        case 'I':\n            read();\n            literal('nfinity');\n            return newToken('numeric', sign * Infinity)\n\n        case 'N':\n            read();\n            literal('aN');\n            return newToken('numeric', NaN)\n        }\n\n        throw invalidChar(read())\n    },\n\n    zero () {\n        switch (c) {\n        case '.':\n            buffer += read();\n            lexState = 'decimalPoint';\n            return\n\n        case 'e':\n        case 'E':\n            buffer += read();\n            lexState = 'decimalExponent';\n            return\n\n        case 'x':\n        case 'X':\n            buffer += read();\n            lexState = 'hexadecimal';\n            return\n        }\n\n        return newToken('numeric', sign * 0)\n    },\n\n    decimalInteger () {\n        switch (c) {\n        case '.':\n            buffer += read();\n            lexState = 'decimalPoint';\n            return\n\n        case 'e':\n        case 'E':\n            buffer += read();\n            lexState = 'decimalExponent';\n            return\n        }\n\n        if (util.isDigit(c)) {\n            buffer += read();\n            return\n        }\n\n        return newToken('numeric', sign * Number(buffer))\n    },\n\n    decimalPointLeading () {\n        if (util.isDigit(c)) {\n            buffer += read();\n            lexState = 'decimalFraction';\n            return\n        }\n\n        throw invalidChar(read())\n    },\n\n    decimalPoint () {\n        switch (c) {\n        case 'e':\n        case 'E':\n            buffer += read();\n            lexState = 'decimalExponent';\n            return\n        }\n\n        if (util.isDigit(c)) {\n            buffer += read();\n            lexState = 'decimalFraction';\n            return\n        }\n\n        return newToken('numeric', sign * Number(buffer))\n    },\n\n    decimalFraction () {\n        switch (c) {\n        case 'e':\n        case 'E':\n            buffer += read();\n            lexState = 'decimalExponent';\n            return\n        }\n\n        if (util.isDigit(c)) {\n            buffer += read();\n            return\n        }\n\n        return newToken('numeric', sign * Number(buffer))\n    },\n\n    decimalExponent () {\n        switch (c) {\n        case '+':\n        case '-':\n            buffer += read();\n            lexState = 'decimalExponentSign';\n            return\n        }\n\n        if (util.isDigit(c)) {\n            buffer += read();\n            lexState = 'decimalExponentInteger';\n            return\n        }\n\n        throw invalidChar(read())\n    },\n\n    decimalExponentSign () {\n        if (util.isDigit(c)) {\n            buffer += read();\n            lexState = 'decimalExponentInteger';\n            return\n        }\n\n        throw invalidChar(read())\n    },\n\n    decimalExponentInteger () {\n        if (util.isDigit(c)) {\n            buffer += read();\n            return\n        }\n\n        return newToken('numeric', sign * Number(buffer))\n    },\n\n    hexadecimal () {\n        if (util.isHexDigit(c)) {\n            buffer += read();\n            lexState = 'hexadecimalInteger';\n            return\n        }\n\n        throw invalidChar(read())\n    },\n\n    hexadecimalInteger () {\n        if (util.isHexDigit(c)) {\n            buffer += read();\n            return\n        }\n\n        return newToken('numeric', sign * Number(buffer))\n    },\n\n    string () {\n        switch (c) {\n        case '\\\\':\n            read();\n            buffer += escape();\n            return\n\n        case '\"':\n            if (doubleQuote) {\n                read();\n                return newToken('string', buffer)\n            }\n\n            buffer += read();\n            return\n\n        case \"'\":\n            if (!doubleQuote) {\n                read();\n                return newToken('string', buffer)\n            }\n\n            buffer += read();\n            return\n\n        case '\\n':\n        case '\\r':\n            throw invalidChar(read())\n\n        case '\\u2028':\n        case '\\u2029':\n            separatorChar(c);\n            break\n\n        case undefined:\n            throw invalidChar(read())\n        }\n\n        buffer += read();\n    },\n\n    start () {\n        switch (c) {\n        case '{':\n        case '[':\n            return newToken('punctuator', read())\n\n        // This code is unreachable since the default lexState handles eof.\n        // case undefined:\n        //     return newToken('eof')\n        }\n\n        lexState = 'value';\n    },\n\n    beforePropertyName () {\n        switch (c) {\n        case '$':\n        case '_':\n            buffer = read();\n            lexState = 'identifierName';\n            return\n\n        case '\\\\':\n            read();\n            lexState = 'identifierNameStartEscape';\n            return\n\n        case '}':\n            return newToken('punctuator', read())\n\n        case '\"':\n        case \"'\":\n            doubleQuote = (read() === '\"');\n            lexState = 'string';\n            return\n        }\n\n        if (util.isIdStartChar(c)) {\n            buffer += read();\n            lexState = 'identifierName';\n            return\n        }\n\n        throw invalidChar(read())\n    },\n\n    afterPropertyName () {\n        if (c === ':') {\n            return newToken('punctuator', read())\n        }\n\n        throw invalidChar(read())\n    },\n\n    beforePropertyValue () {\n        lexState = 'value';\n    },\n\n    afterPropertyValue () {\n        switch (c) {\n        case ',':\n        case '}':\n            return newToken('punctuator', read())\n        }\n\n        throw invalidChar(read())\n    },\n\n    beforeArrayValue () {\n        if (c === ']') {\n            return newToken('punctuator', read())\n        }\n\n        lexState = 'value';\n    },\n\n    afterArrayValue () {\n        switch (c) {\n        case ',':\n        case ']':\n            return newToken('punctuator', read())\n        }\n\n        throw invalidChar(read())\n    },\n\n    end () {\n        // This code is unreachable since it's handled by the default lexState.\n        // if (c === undefined) {\n        //     read()\n        //     return newToken('eof')\n        // }\n\n        throw invalidChar(read())\n    },\n};\n\nfunction newToken (type, value) {\n    return {\n        type,\n        value,\n        line,\n        column,\n    }\n}\n\nfunction literal (s) {\n    for (const c of s) {\n        const p = peek();\n\n        if (p !== c) {\n            throw invalidChar(read())\n        }\n\n        read();\n    }\n}\n\nfunction escape () {\n    const c = peek();\n    switch (c) {\n    case 'b':\n        read();\n        return '\\b'\n\n    case 'f':\n        read();\n        return '\\f'\n\n    case 'n':\n        read();\n        return '\\n'\n\n    case 'r':\n        read();\n        return '\\r'\n\n    case 't':\n        read();\n        return '\\t'\n\n    case 'v':\n        read();\n        return '\\v'\n\n    case '0':\n        read();\n        if (util.isDigit(peek())) {\n            throw invalidChar(read())\n        }\n\n        return '\\0'\n\n    case 'x':\n        read();\n        return hexEscape()\n\n    case 'u':\n        read();\n        return unicodeEscape()\n\n    case '\\n':\n    case '\\u2028':\n    case '\\u2029':\n        read();\n        return ''\n\n    case '\\r':\n        read();\n        if (peek() === '\\n') {\n            read();\n        }\n\n        return ''\n\n    case '1':\n    case '2':\n    case '3':\n    case '4':\n    case '5':\n    case '6':\n    case '7':\n    case '8':\n    case '9':\n        throw invalidChar(read())\n\n    case undefined:\n        throw invalidChar(read())\n    }\n\n    return read()\n}\n\nfunction hexEscape () {\n    let buffer = '';\n    let c = peek();\n\n    if (!util.isHexDigit(c)) {\n        throw invalidChar(read())\n    }\n\n    buffer += read();\n\n    c = peek();\n    if (!util.isHexDigit(c)) {\n        throw invalidChar(read())\n    }\n\n    buffer += read();\n\n    return String.fromCodePoint(parseInt(buffer, 16))\n}\n\nfunction unicodeEscape () {\n    let buffer = '';\n    let count = 4;\n\n    while (count-- > 0) {\n        const c = peek();\n        if (!util.isHexDigit(c)) {\n            throw invalidChar(read())\n        }\n\n        buffer += read();\n    }\n\n    return String.fromCodePoint(parseInt(buffer, 16))\n}\n\nconst parseStates = {\n    start () {\n        if (token.type === 'eof') {\n            throw invalidEOF()\n        }\n\n        push();\n    },\n\n    beforePropertyName () {\n        switch (token.type) {\n        case 'identifier':\n        case 'string':\n            key = token.value;\n            parseState = 'afterPropertyName';\n            return\n\n        case 'punctuator':\n            // This code is unreachable since it's handled by the lexState.\n            // if (token.value !== '}') {\n            //     throw invalidToken()\n            // }\n\n            pop();\n            return\n\n        case 'eof':\n            throw invalidEOF()\n        }\n\n        // This code is unreachable since it's handled by the lexState.\n        // throw invalidToken()\n    },\n\n    afterPropertyName () {\n        // This code is unreachable since it's handled by the lexState.\n        // if (token.type !== 'punctuator' || token.value !== ':') {\n        //     throw invalidToken()\n        // }\n\n        if (token.type === 'eof') {\n            throw invalidEOF()\n        }\n\n        parseState = 'beforePropertyValue';\n    },\n\n    beforePropertyValue () {\n        if (token.type === 'eof') {\n            throw invalidEOF()\n        }\n\n        push();\n    },\n\n    beforeArrayValue () {\n        if (token.type === 'eof') {\n            throw invalidEOF()\n        }\n\n        if (token.type === 'punctuator' && token.value === ']') {\n            pop();\n            return\n        }\n\n        push();\n    },\n\n    afterPropertyValue () {\n        // This code is unreachable since it's handled by the lexState.\n        // if (token.type !== 'punctuator') {\n        //     throw invalidToken()\n        // }\n\n        if (token.type === 'eof') {\n            throw invalidEOF()\n        }\n\n        switch (token.value) {\n        case ',':\n            parseState = 'beforePropertyName';\n            return\n\n        case '}':\n            pop();\n        }\n\n        // This code is unreachable since it's handled by the lexState.\n        // throw invalidToken()\n    },\n\n    afterArrayValue () {\n        // This code is unreachable since it's handled by the lexState.\n        // if (token.type !== 'punctuator') {\n        //     throw invalidToken()\n        // }\n\n        if (token.type === 'eof') {\n            throw invalidEOF()\n        }\n\n        switch (token.value) {\n        case ',':\n            parseState = 'beforeArrayValue';\n            return\n\n        case ']':\n            pop();\n        }\n\n        // This code is unreachable since it's handled by the lexState.\n        // throw invalidToken()\n    },\n\n    end () {\n        // This code is unreachable since it's handled by the lexState.\n        // if (token.type !== 'eof') {\n        //     throw invalidToken()\n        // }\n    },\n};\n\nfunction push () {\n    let value;\n\n    switch (token.type) {\n    case 'punctuator':\n        switch (token.value) {\n        case '{':\n            value = {};\n            break\n\n        case '[':\n            value = [];\n            break\n        }\n\n        break\n\n    case 'null':\n    case 'boolean':\n    case 'numeric':\n    case 'string':\n        value = token.value;\n        break\n\n    // This code is unreachable.\n    // default:\n    //     throw invalidToken()\n    }\n\n    if (root === undefined) {\n        root = value;\n    } else {\n        const parent = stack[stack.length - 1];\n        if (Array.isArray(parent)) {\n            parent.push(value);\n        } else {\n            Object.defineProperty(parent, key, {\n                value,\n                writable: true,\n                enumerable: true,\n                configurable: true,\n            });\n        }\n    }\n\n    if (value !== null && typeof value === 'object') {\n        stack.push(value);\n\n        if (Array.isArray(value)) {\n            parseState = 'beforeArrayValue';\n        } else {\n            parseState = 'beforePropertyName';\n        }\n    } else {\n        const current = stack[stack.length - 1];\n        if (current == null) {\n            parseState = 'end';\n        } else if (Array.isArray(current)) {\n            parseState = 'afterArrayValue';\n        } else {\n            parseState = 'afterPropertyValue';\n        }\n    }\n}\n\nfunction pop () {\n    stack.pop();\n\n    const current = stack[stack.length - 1];\n    if (current == null) {\n        parseState = 'end';\n    } else if (Array.isArray(current)) {\n        parseState = 'afterArrayValue';\n    } else {\n        parseState = 'afterPropertyValue';\n    }\n}\n\n// This code is unreachable.\n// function invalidParseState () {\n//     return new Error(`JSON5: invalid parse state '${parseState}'`)\n// }\n\n// This code is unreachable.\n// function invalidLexState (state) {\n//     return new Error(`JSON5: invalid lex state '${state}'`)\n// }\n\nfunction invalidChar (c) {\n    if (c === undefined) {\n        return syntaxError(`JSON5: invalid end of input at ${line}:${column}`)\n    }\n\n    return syntaxError(`JSON5: invalid character '${formatChar(c)}' at ${line}:${column}`)\n}\n\nfunction invalidEOF () {\n    return syntaxError(`JSON5: invalid end of input at ${line}:${column}`)\n}\n\n// This code is unreachable.\n// function invalidToken () {\n//     if (token.type === 'eof') {\n//         return syntaxError(`JSON5: invalid end of input at ${line}:${column}`)\n//     }\n\n//     const c = String.fromCodePoint(token.value.codePointAt(0))\n//     return syntaxError(`JSON5: invalid character '${formatChar(c)}' at ${line}:${column}`)\n// }\n\nfunction invalidIdentifier () {\n    column -= 5;\n    return syntaxError(`JSON5: invalid identifier character at ${line}:${column}`)\n}\n\nfunction separatorChar (c) {\n    console.warn(`JSON5: '${formatChar(c)}' in strings is not valid ECMAScript; consider escaping`);\n}\n\nfunction formatChar (c) {\n    const replacements = {\n        \"'\": \"\\\\'\",\n        '\"': '\\\\\"',\n        '\\\\': '\\\\\\\\',\n        '\\b': '\\\\b',\n        '\\f': '\\\\f',\n        '\\n': '\\\\n',\n        '\\r': '\\\\r',\n        '\\t': '\\\\t',\n        '\\v': '\\\\v',\n        '\\0': '\\\\0',\n        '\\u2028': '\\\\u2028',\n        '\\u2029': '\\\\u2029',\n    };\n\n    if (replacements[c]) {\n        return replacements[c]\n    }\n\n    if (c < ' ') {\n        const hexString = c.charCodeAt(0).toString(16);\n        return '\\\\x' + ('00' + hexString).substring(hexString.length)\n    }\n\n    return c\n}\n\nfunction syntaxError (message) {\n    const err = new SyntaxError(message);\n    err.lineNumber = line;\n    err.columnNumber = column;\n    return err\n}\n\nvar stringify = function stringify (value, replacer, space) {\n    const stack = [];\n    let indent = '';\n    let propertyList;\n    let replacerFunc;\n    let gap = '';\n    let quote;\n\n    if (\n        replacer != null &&\n        typeof replacer === 'object' &&\n        !Array.isArray(replacer)\n    ) {\n        space = replacer.space;\n        quote = replacer.quote;\n        replacer = replacer.replacer;\n    }\n\n    if (typeof replacer === 'function') {\n        replacerFunc = replacer;\n    } else if (Array.isArray(replacer)) {\n        propertyList = [];\n        for (const v of replacer) {\n            let item;\n\n            if (typeof v === 'string') {\n                item = v;\n            } else if (\n                typeof v === 'number' ||\n                v instanceof String ||\n                v instanceof Number\n            ) {\n                item = String(v);\n            }\n\n            if (item !== undefined && propertyList.indexOf(item) < 0) {\n                propertyList.push(item);\n            }\n        }\n    }\n\n    if (space instanceof Number) {\n        space = Number(space);\n    } else if (space instanceof String) {\n        space = String(space);\n    }\n\n    if (typeof space === 'number') {\n        if (space > 0) {\n            space = Math.min(10, Math.floor(space));\n            gap = '          '.substr(0, space);\n        }\n    } else if (typeof space === 'string') {\n        gap = space.substr(0, 10);\n    }\n\n    return serializeProperty('', {'': value})\n\n    function serializeProperty (key, holder) {\n        let value = holder[key];\n        if (value != null) {\n            if (typeof value.toJSON5 === 'function') {\n                value = value.toJSON5(key);\n            } else if (typeof value.toJSON === 'function') {\n                value = value.toJSON(key);\n            }\n        }\n\n        if (replacerFunc) {\n            value = replacerFunc.call(holder, key, value);\n        }\n\n        if (value instanceof Number) {\n            value = Number(value);\n        } else if (value instanceof String) {\n            value = String(value);\n        } else if (value instanceof Boolean) {\n            value = value.valueOf();\n        }\n\n        switch (value) {\n        case null: return 'null'\n        case true: return 'true'\n        case false: return 'false'\n        }\n\n        if (typeof value === 'string') {\n            return quoteString(value, false)\n        }\n\n        if (typeof value === 'number') {\n            return String(value)\n        }\n\n        if (typeof value === 'object') {\n            return Array.isArray(value) ? serializeArray(value) : serializeObject(value)\n        }\n\n        return undefined\n    }\n\n    function quoteString (value) {\n        const quotes = {\n            \"'\": 0.1,\n            '\"': 0.2,\n        };\n\n        const replacements = {\n            \"'\": \"\\\\'\",\n            '\"': '\\\\\"',\n            '\\\\': '\\\\\\\\',\n            '\\b': '\\\\b',\n            '\\f': '\\\\f',\n            '\\n': '\\\\n',\n            '\\r': '\\\\r',\n            '\\t': '\\\\t',\n            '\\v': '\\\\v',\n            '\\0': '\\\\0',\n            '\\u2028': '\\\\u2028',\n            '\\u2029': '\\\\u2029',\n        };\n\n        let product = '';\n\n        for (let i = 0; i < value.length; i++) {\n            const c = value[i];\n            switch (c) {\n            case \"'\":\n            case '\"':\n                quotes[c]++;\n                product += c;\n                continue\n\n            case '\\0':\n                if (util.isDigit(value[i + 1])) {\n                    product += '\\\\x00';\n                    continue\n                }\n            }\n\n            if (replacements[c]) {\n                product += replacements[c];\n                continue\n            }\n\n            if (c < ' ') {\n                let hexString = c.charCodeAt(0).toString(16);\n                product += '\\\\x' + ('00' + hexString).substring(hexString.length);\n                continue\n            }\n\n            product += c;\n        }\n\n        const quoteChar = quote || Object.keys(quotes).reduce((a, b) => (quotes[a] < quotes[b]) ? a : b);\n\n        product = product.replace(new RegExp(quoteChar, 'g'), replacements[quoteChar]);\n\n        return quoteChar + product + quoteChar\n    }\n\n    function serializeObject (value) {\n        if (stack.indexOf(value) >= 0) {\n            throw TypeError('Converting circular structure to JSON5')\n        }\n\n        stack.push(value);\n\n        let stepback = indent;\n        indent = indent + gap;\n\n        let keys = propertyList || Object.keys(value);\n        let partial = [];\n        for (const key of keys) {\n            const propertyString = serializeProperty(key, value);\n            if (propertyString !== undefined) {\n                let member = serializeKey(key) + ':';\n                if (gap !== '') {\n                    member += ' ';\n                }\n                member += propertyString;\n                partial.push(member);\n            }\n        }\n\n        let final;\n        if (partial.length === 0) {\n            final = '{}';\n        } else {\n            let properties;\n            if (gap === '') {\n                properties = partial.join(',');\n                final = '{' + properties + '}';\n            } else {\n                let separator = ',\\n' + indent;\n                properties = partial.join(separator);\n                final = '{\\n' + indent + properties + ',\\n' + stepback + '}';\n            }\n        }\n\n        stack.pop();\n        indent = stepback;\n        return final\n    }\n\n    function serializeKey (key) {\n        if (key.length === 0) {\n            return quoteString(key, true)\n        }\n\n        const firstChar = String.fromCodePoint(key.codePointAt(0));\n        if (!util.isIdStartChar(firstChar)) {\n            return quoteString(key, true)\n        }\n\n        for (let i = firstChar.length; i < key.length; i++) {\n            if (!util.isIdContinueChar(String.fromCodePoint(key.codePointAt(i)))) {\n                return quoteString(key, true)\n            }\n        }\n\n        return key\n    }\n\n    function serializeArray (value) {\n        if (stack.indexOf(value) >= 0) {\n            throw TypeError('Converting circular structure to JSON5')\n        }\n\n        stack.push(value);\n\n        let stepback = indent;\n        indent = indent + gap;\n\n        let partial = [];\n        for (let i = 0; i < value.length; i++) {\n            const propertyString = serializeProperty(String(i), value);\n            partial.push((propertyString !== undefined) ? propertyString : 'null');\n        }\n\n        let final;\n        if (partial.length === 0) {\n            final = '[]';\n        } else {\n            if (gap === '') {\n                let properties = partial.join(',');\n                final = '[' + properties + ']';\n            } else {\n                let separator = ',\\n' + indent;\n                let properties = partial.join(separator);\n                final = '[\\n' + indent + properties + ',\\n' + stepback + ']';\n            }\n        }\n\n        stack.pop();\n        indent = stepback;\n        return final\n    }\n};\n\nconst JSON5 = {\n    parse,\n    stringify,\n};\n\nvar lib = JSON5;\n\nexport default lib;\n"], "mappings": ";;;AACA,IAAI,kBAAkB;AACtB,IAAI,WAAW;AACf,IAAI,cAAc;AAElB,IAAI,UAAU;AAAA,EACb;AAAA,EACA;AAAA,EACA;AACD;AAEA,IAAI,OAAO;AAAA,EACP,iBAAkBA,IAAG;AACjB,WAAO,OAAOA,OAAM,YAAY,QAAQ,gBAAgB,KAAKA,EAAC;AAAA,EAClE;AAAA,EAEA,cAAeA,IAAG;AACd,WAAO,OAAOA,OAAM,aACfA,MAAK,OAAOA,MAAK,OACrBA,MAAK,OAAOA,MAAK,OACjBA,OAAM,OAASA,OAAM,OACtB,QAAQ,SAAS,KAAKA,EAAC;AAAA,EAE3B;AAAA,EAEA,iBAAkBA,IAAG;AACjB,WAAO,OAAOA,OAAM,aACfA,MAAK,OAAOA,MAAK,OACrBA,MAAK,OAAOA,MAAK,OACjBA,MAAK,OAAOA,MAAK,OACjBA,OAAM,OAASA,OAAM,OACrBA,OAAM,OAAcA,OAAM,OAC3B,QAAQ,YAAY,KAAKA,EAAC;AAAA,EAE9B;AAAA,EAEA,QAASA,IAAG;AACR,WAAO,OAAOA,OAAM,YAAY,QAAQ,KAAKA,EAAC;AAAA,EAClD;AAAA,EAEA,WAAYA,IAAG;AACX,WAAO,OAAOA,OAAM,YAAY,cAAc,KAAKA,EAAC;AAAA,EACxD;AACJ;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,QAAQ,SAASC,OAAO,MAAM,SAAS;AACvC,WAAS,OAAO,IAAI;AACpB,eAAa;AACb,UAAQ,CAAC;AACT,QAAM;AACN,SAAO;AACP,WAAS;AACT,UAAQ;AACR,QAAM;AACN,SAAO;AAEP,KAAG;AACC,YAAQ,IAAI;AAOZ,gBAAY,UAAU,EAAE;AAAA,EAC5B,SAAS,MAAM,SAAS;AAExB,MAAI,OAAO,YAAY,YAAY;AAC/B,WAAO,YAAY,EAAC,IAAI,KAAI,GAAG,IAAI,OAAO;AAAA,EAC9C;AAEA,SAAO;AACX;AAEA,SAAS,YAAa,QAAQ,MAAM,SAAS;AACzC,QAAM,QAAQ,OAAO,IAAI;AACzB,MAAI,SAAS,QAAQ,OAAO,UAAU,UAAU;AAC5C,QAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,cAAMC,OAAM,OAAO,CAAC;AACpB,cAAM,cAAc,YAAY,OAAOA,MAAK,OAAO;AACnD,YAAI,gBAAgB,QAAW;AAC3B,iBAAO,MAAMA,IAAG;AAAA,QACpB,OAAO;AACH,iBAAO,eAAe,OAAOA,MAAK;AAAA,YAC9B,OAAO;AAAA,YACP,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ,OAAO;AACH,iBAAWA,QAAO,OAAO;AACrB,cAAM,cAAc,YAAY,OAAOA,MAAK,OAAO;AACnD,YAAI,gBAAgB,QAAW;AAC3B,iBAAO,MAAMA,IAAG;AAAA,QACpB,OAAO;AACH,iBAAO,eAAe,OAAOA,MAAK;AAAA,YAC9B,OAAO;AAAA,YACP,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,SAAO,QAAQ,KAAK,QAAQ,MAAM,KAAK;AAC3C;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,MAAO;AACZ,aAAW;AACX,WAAS;AACT,gBAAc;AACd,SAAO;AAEP,aAAS;AACL,QAAI,KAAK;AAOT,UAAMC,SAAQ,UAAU,QAAQ,EAAE;AAClC,QAAIA,QAAO;AACP,aAAOA;AAAA,IACX;AAAA,EACJ;AACJ;AAEA,SAAS,OAAQ;AACb,MAAI,OAAO,GAAG,GAAG;AACb,WAAO,OAAO,cAAc,OAAO,YAAY,GAAG,CAAC;AAAA,EACvD;AACJ;AAEA,SAAS,OAAQ;AACb,QAAMH,KAAI,KAAK;AAEf,MAAIA,OAAM,MAAM;AACZ;AACA,aAAS;AAAA,EACb,WAAWA,IAAG;AACV,cAAUA,GAAE;AAAA,EAChB,OAAO;AACH;AAAA,EACJ;AAEA,MAAIA,IAAG;AACH,WAAOA,GAAE;AAAA,EACb;AAEA,SAAOA;AACX;AAEA,IAAM,YAAY;AAAA,EACd,UAAW;AACP,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,aAAK;AACL;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,eAAO,SAAS,KAAK;AAAA,IACzB;AAEA,QAAI,KAAK,iBAAiB,CAAC,GAAG;AAC1B,WAAK;AACL;AAAA,IACJ;AAOA,WAAO,UAAU,UAAU,EAAE;AAAA,EACjC;AAAA,EAEA,UAAW;AACP,YAAQ,GAAG;AAAA,MACX,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,IACJ;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,mBAAoB;AAChB,YAAQ,GAAG;AAAA,MACX,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,cAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AAEA,SAAK;AAAA,EACT;AAAA,EAEA,2BAA4B;AACxB,YAAQ,GAAG;AAAA,MACX,KAAK;AACD,aAAK;AACL;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,cAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AAEA,SAAK;AACL,eAAW;AAAA,EACf;AAAA,EAEA,oBAAqB;AACjB,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,eAAO,SAAS,KAAK;AAAA,IACzB;AAEA,SAAK;AAAA,EACT;AAAA,EAEA,QAAS;AACL,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,eAAO,SAAS,cAAc,KAAK,CAAC;AAAA,MAExC,KAAK;AACD,aAAK;AACL,gBAAQ,KAAK;AACb,eAAO,SAAS,QAAQ,IAAI;AAAA,MAEhC,KAAK;AACD,aAAK;AACL,gBAAQ,KAAK;AACb,eAAO,SAAS,WAAW,IAAI;AAAA,MAEnC,KAAK;AACD,aAAK;AACL,gBAAQ,MAAM;AACd,eAAO,SAAS,WAAW,KAAK;AAAA,MAEpC,KAAK;AAAA,MACL,KAAK;AACD,YAAI,KAAK,MAAM,KAAK;AAChB,iBAAO;AAAA,QACX;AAEA,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,gBAAQ,SAAS;AACjB,eAAO,SAAS,WAAW,QAAQ;AAAA,MAEvC,KAAK;AACD,aAAK;AACL,gBAAQ,IAAI;AACZ,eAAO,SAAS,WAAW,GAAG;AAAA,MAElC,KAAK;AAAA,MACL,KAAK;AACD,sBAAe,KAAK,MAAM;AAC1B,iBAAS;AACT,mBAAW;AACX;AAAA,IACJ;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,4BAA6B;AACzB,QAAI,MAAM,KAAK;AACX,YAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AAEA,SAAK;AACL,UAAM,IAAI,cAAc;AACxB,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD;AAAA,MAEJ;AACI,YAAI,CAAC,KAAK,cAAc,CAAC,GAAG;AACxB,gBAAM,kBAAkB;AAAA,QAC5B;AAEA;AAAA,IACJ;AAEA,cAAU;AACV,eAAW;AAAA,EACf;AAAA,EAEA,iBAAkB;AACd,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,kBAAU,KAAK;AACf;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,IACJ;AAEA,QAAI,KAAK,iBAAiB,CAAC,GAAG;AAC1B,gBAAU,KAAK;AACf;AAAA,IACJ;AAEA,WAAO,SAAS,cAAc,MAAM;AAAA,EACxC;AAAA,EAEA,uBAAwB;AACpB,QAAI,MAAM,KAAK;AACX,YAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AAEA,SAAK;AACL,UAAM,IAAI,cAAc;AACxB,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD;AAAA,MAEJ;AACI,YAAI,CAAC,KAAK,iBAAiB,CAAC,GAAG;AAC3B,gBAAM,kBAAkB;AAAA,QAC5B;AAEA;AAAA,IACJ;AAEA,cAAU;AACV,eAAW;AAAA,EACf;AAAA,EAEA,OAAQ;AACJ,YAAQ,GAAG;AAAA,MACX,KAAK;AACD,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,gBAAQ,SAAS;AACjB,eAAO,SAAS,WAAW,OAAO,QAAQ;AAAA,MAE9C,KAAK;AACD,aAAK;AACL,gBAAQ,IAAI;AACZ,eAAO,SAAS,WAAW,GAAG;AAAA,IAClC;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,OAAQ;AACJ,YAAQ,GAAG;AAAA,MACX,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,IACJ;AAEA,WAAO,SAAS,WAAW,OAAO,CAAC;AAAA,EACvC;AAAA,EAEA,iBAAkB;AACd,YAAQ,GAAG;AAAA,MACX,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,IACJ;AAEA,QAAI,KAAK,QAAQ,CAAC,GAAG;AACjB,gBAAU,KAAK;AACf;AAAA,IACJ;AAEA,WAAO,SAAS,WAAW,OAAO,OAAO,MAAM,CAAC;AAAA,EACpD;AAAA,EAEA,sBAAuB;AACnB,QAAI,KAAK,QAAQ,CAAC,GAAG;AACjB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACJ;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,eAAgB;AACZ,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,IACJ;AAEA,QAAI,KAAK,QAAQ,CAAC,GAAG;AACjB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACJ;AAEA,WAAO,SAAS,WAAW,OAAO,OAAO,MAAM,CAAC;AAAA,EACpD;AAAA,EAEA,kBAAmB;AACf,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,IACJ;AAEA,QAAI,KAAK,QAAQ,CAAC,GAAG;AACjB,gBAAU,KAAK;AACf;AAAA,IACJ;AAEA,WAAO,SAAS,WAAW,OAAO,OAAO,MAAM,CAAC;AAAA,EACpD;AAAA,EAEA,kBAAmB;AACf,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,IACJ;AAEA,QAAI,KAAK,QAAQ,CAAC,GAAG;AACjB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACJ;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,sBAAuB;AACnB,QAAI,KAAK,QAAQ,CAAC,GAAG;AACjB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACJ;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,yBAA0B;AACtB,QAAI,KAAK,QAAQ,CAAC,GAAG;AACjB,gBAAU,KAAK;AACf;AAAA,IACJ;AAEA,WAAO,SAAS,WAAW,OAAO,OAAO,MAAM,CAAC;AAAA,EACpD;AAAA,EAEA,cAAe;AACX,QAAI,KAAK,WAAW,CAAC,GAAG;AACpB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACJ;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,qBAAsB;AAClB,QAAI,KAAK,WAAW,CAAC,GAAG;AACpB,gBAAU,KAAK;AACf;AAAA,IACJ;AAEA,WAAO,SAAS,WAAW,OAAO,OAAO,MAAM,CAAC;AAAA,EACpD;AAAA,EAEA,SAAU;AACN,YAAQ,GAAG;AAAA,MACX,KAAK;AACD,aAAK;AACL,kBAAU,OAAO;AACjB;AAAA,MAEJ,KAAK;AACD,YAAI,aAAa;AACb,eAAK;AACL,iBAAO,SAAS,UAAU,MAAM;AAAA,QACpC;AAEA,kBAAU,KAAK;AACf;AAAA,MAEJ,KAAK;AACD,YAAI,CAAC,aAAa;AACd,eAAK;AACL,iBAAO,SAAS,UAAU,MAAM;AAAA,QACpC;AAEA,kBAAU,KAAK;AACf;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AACD,cAAM,YAAY,KAAK,CAAC;AAAA,MAE5B,KAAK;AAAA,MACL,KAAK;AACD,sBAAc,CAAC;AACf;AAAA,MAEJ,KAAK;AACD,cAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AAEA,cAAU,KAAK;AAAA,EACnB;AAAA,EAEA,QAAS;AACL,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,eAAO,SAAS,cAAc,KAAK,CAAC;AAAA,IAKxC;AAEA,eAAW;AAAA,EACf;AAAA,EAEA,qBAAsB;AAClB,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,eAAO,SAAS,cAAc,KAAK,CAAC;AAAA,MAExC,KAAK;AAAA,MACL,KAAK;AACD,sBAAe,KAAK,MAAM;AAC1B,mBAAW;AACX;AAAA,IACJ;AAEA,QAAI,KAAK,cAAc,CAAC,GAAG;AACvB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACJ;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,oBAAqB;AACjB,QAAI,MAAM,KAAK;AACX,aAAO,SAAS,cAAc,KAAK,CAAC;AAAA,IACxC;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,sBAAuB;AACnB,eAAW;AAAA,EACf;AAAA,EAEA,qBAAsB;AAClB,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,eAAO,SAAS,cAAc,KAAK,CAAC;AAAA,IACxC;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,mBAAoB;AAChB,QAAI,MAAM,KAAK;AACX,aAAO,SAAS,cAAc,KAAK,CAAC;AAAA,IACxC;AAEA,eAAW;AAAA,EACf;AAAA,EAEA,kBAAmB;AACf,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,eAAO,SAAS,cAAc,KAAK,CAAC;AAAA,IACxC;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,MAAO;AAOH,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AACJ;AAEA,SAAS,SAAU,MAAM,OAAO;AAC5B,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,SAAS,QAAS,GAAG;AACjB,aAAWA,MAAK,GAAG;AACf,UAAM,IAAI,KAAK;AAEf,QAAI,MAAMA,IAAG;AACT,YAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AAEA,SAAK;AAAA,EACT;AACJ;AAEA,SAAS,SAAU;AACf,QAAMA,KAAI,KAAK;AACf,UAAQA,IAAG;AAAA,IACX,KAAK;AACD,WAAK;AACL,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,UAAI,KAAK,QAAQ,KAAK,CAAC,GAAG;AACtB,cAAM,YAAY,KAAK,CAAC;AAAA,MAC5B;AAEA,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,aAAO,UAAU;AAAA,IAErB,KAAK;AACD,WAAK;AACL,aAAO,cAAc;AAAA,IAEzB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,WAAK;AACL,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,UAAI,KAAK,MAAM,MAAM;AACjB,aAAK;AAAA,MACT;AAEA,aAAO;AAAA,IAEX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,YAAM,YAAY,KAAK,CAAC;AAAA,IAE5B,KAAK;AACD,YAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAEA,SAAO,KAAK;AAChB;AAEA,SAAS,YAAa;AAClB,MAAII,UAAS;AACb,MAAIJ,KAAI,KAAK;AAEb,MAAI,CAAC,KAAK,WAAWA,EAAC,GAAG;AACrB,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAEA,EAAAI,WAAU,KAAK;AAEf,EAAAJ,KAAI,KAAK;AACT,MAAI,CAAC,KAAK,WAAWA,EAAC,GAAG;AACrB,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAEA,EAAAI,WAAU,KAAK;AAEf,SAAO,OAAO,cAAc,SAASA,SAAQ,EAAE,CAAC;AACpD;AAEA,SAAS,gBAAiB;AACtB,MAAIA,UAAS;AACb,MAAI,QAAQ;AAEZ,SAAO,UAAU,GAAG;AAChB,UAAMJ,KAAI,KAAK;AACf,QAAI,CAAC,KAAK,WAAWA,EAAC,GAAG;AACrB,YAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AAEA,IAAAI,WAAU,KAAK;AAAA,EACnB;AAEA,SAAO,OAAO,cAAc,SAASA,SAAQ,EAAE,CAAC;AACpD;AAEA,IAAM,cAAc;AAAA,EAChB,QAAS;AACL,QAAI,MAAM,SAAS,OAAO;AACtB,YAAM,WAAW;AAAA,IACrB;AAEA,SAAK;AAAA,EACT;AAAA,EAEA,qBAAsB;AAClB,YAAQ,MAAM,MAAM;AAAA,MACpB,KAAK;AAAA,MACL,KAAK;AACD,cAAM,MAAM;AACZ,qBAAa;AACb;AAAA,MAEJ,KAAK;AAMD,YAAI;AACJ;AAAA,MAEJ,KAAK;AACD,cAAM,WAAW;AAAA,IACrB;AAAA,EAIJ;AAAA,EAEA,oBAAqB;AAMjB,QAAI,MAAM,SAAS,OAAO;AACtB,YAAM,WAAW;AAAA,IACrB;AAEA,iBAAa;AAAA,EACjB;AAAA,EAEA,sBAAuB;AACnB,QAAI,MAAM,SAAS,OAAO;AACtB,YAAM,WAAW;AAAA,IACrB;AAEA,SAAK;AAAA,EACT;AAAA,EAEA,mBAAoB;AAChB,QAAI,MAAM,SAAS,OAAO;AACtB,YAAM,WAAW;AAAA,IACrB;AAEA,QAAI,MAAM,SAAS,gBAAgB,MAAM,UAAU,KAAK;AACpD,UAAI;AACJ;AAAA,IACJ;AAEA,SAAK;AAAA,EACT;AAAA,EAEA,qBAAsB;AAMlB,QAAI,MAAM,SAAS,OAAO;AACtB,YAAM,WAAW;AAAA,IACrB;AAEA,YAAQ,MAAM,OAAO;AAAA,MACrB,KAAK;AACD,qBAAa;AACb;AAAA,MAEJ,KAAK;AACD,YAAI;AAAA,IACR;AAAA,EAIJ;AAAA,EAEA,kBAAmB;AAMf,QAAI,MAAM,SAAS,OAAO;AACtB,YAAM,WAAW;AAAA,IACrB;AAEA,YAAQ,MAAM,OAAO;AAAA,MACrB,KAAK;AACD,qBAAa;AACb;AAAA,MAEJ,KAAK;AACD,YAAI;AAAA,IACR;AAAA,EAIJ;AAAA,EAEA,MAAO;AAAA,EAKP;AACJ;AAEA,SAAS,OAAQ;AACb,MAAI;AAEJ,UAAQ,MAAM,MAAM;AAAA,IACpB,KAAK;AACD,cAAQ,MAAM,OAAO;AAAA,QACrB,KAAK;AACD,kBAAQ,CAAC;AACT;AAAA,QAEJ,KAAK;AACD,kBAAQ,CAAC;AACT;AAAA,MACJ;AAEA;AAAA,IAEJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,cAAQ,MAAM;AACd;AAAA,EAKJ;AAEA,MAAI,SAAS,QAAW;AACpB,WAAO;AAAA,EACX,OAAO;AACH,UAAM,SAAS,MAAM,MAAM,SAAS,CAAC;AACrC,QAAI,MAAM,QAAQ,MAAM,GAAG;AACvB,aAAO,KAAK,KAAK;AAAA,IACrB,OAAO;AACH,aAAO,eAAe,QAAQ,KAAK;AAAA,QAC/B;AAAA,QACA,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,MAClB,CAAC;AAAA,IACL;AAAA,EACJ;AAEA,MAAI,UAAU,QAAQ,OAAO,UAAU,UAAU;AAC7C,UAAM,KAAK,KAAK;AAEhB,QAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,mBAAa;AAAA,IACjB,OAAO;AACH,mBAAa;AAAA,IACjB;AAAA,EACJ,OAAO;AACH,UAAM,UAAU,MAAM,MAAM,SAAS,CAAC;AACtC,QAAI,WAAW,MAAM;AACjB,mBAAa;AAAA,IACjB,WAAW,MAAM,QAAQ,OAAO,GAAG;AAC/B,mBAAa;AAAA,IACjB,OAAO;AACH,mBAAa;AAAA,IACjB;AAAA,EACJ;AACJ;AAEA,SAAS,MAAO;AACZ,QAAM,IAAI;AAEV,QAAM,UAAU,MAAM,MAAM,SAAS,CAAC;AACtC,MAAI,WAAW,MAAM;AACjB,iBAAa;AAAA,EACjB,WAAW,MAAM,QAAQ,OAAO,GAAG;AAC/B,iBAAa;AAAA,EACjB,OAAO;AACH,iBAAa;AAAA,EACjB;AACJ;AAYA,SAAS,YAAaJ,IAAG;AACrB,MAAIA,OAAM,QAAW;AACjB,WAAO,YAAY,kCAAkC,IAAI,IAAI,MAAM,EAAE;AAAA,EACzE;AAEA,SAAO,YAAY,6BAA6B,WAAWA,EAAC,CAAC,QAAQ,IAAI,IAAI,MAAM,EAAE;AACzF;AAEA,SAAS,aAAc;AACnB,SAAO,YAAY,kCAAkC,IAAI,IAAI,MAAM,EAAE;AACzE;AAYA,SAAS,oBAAqB;AAC1B,YAAU;AACV,SAAO,YAAY,0CAA0C,IAAI,IAAI,MAAM,EAAE;AACjF;AAEA,SAAS,cAAeA,IAAG;AACvB,UAAQ,KAAK,WAAW,WAAWA,EAAC,CAAC,yDAAyD;AAClG;AAEA,SAAS,WAAYA,IAAG;AACpB,QAAM,eAAe;AAAA,IACjB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,KAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,EACd;AAEA,MAAI,aAAaA,EAAC,GAAG;AACjB,WAAO,aAAaA,EAAC;AAAA,EACzB;AAEA,MAAIA,KAAI,KAAK;AACT,UAAM,YAAYA,GAAE,WAAW,CAAC,EAAE,SAAS,EAAE;AAC7C,WAAO,SAAS,OAAO,WAAW,UAAU,UAAU,MAAM;AAAA,EAChE;AAEA,SAAOA;AACX;AAEA,SAAS,YAAa,SAAS;AAC3B,QAAM,MAAM,IAAI,YAAY,OAAO;AACnC,MAAI,aAAa;AACjB,MAAI,eAAe;AACnB,SAAO;AACX;AAEA,IAAI,YAAY,SAASK,WAAW,OAAO,UAAU,OAAO;AACxD,QAAMC,SAAQ,CAAC;AACf,MAAI,SAAS;AACb,MAAI;AACJ,MAAI;AACJ,MAAI,MAAM;AACV,MAAI;AAEJ,MACI,YAAY,QACZ,OAAO,aAAa,YACpB,CAAC,MAAM,QAAQ,QAAQ,GACzB;AACE,YAAQ,SAAS;AACjB,YAAQ,SAAS;AACjB,eAAW,SAAS;AAAA,EACxB;AAEA,MAAI,OAAO,aAAa,YAAY;AAChC,mBAAe;AAAA,EACnB,WAAW,MAAM,QAAQ,QAAQ,GAAG;AAChC,mBAAe,CAAC;AAChB,eAAW,KAAK,UAAU;AACtB,UAAI;AAEJ,UAAI,OAAO,MAAM,UAAU;AACvB,eAAO;AAAA,MACX,WACI,OAAO,MAAM,YACb,aAAa,UACb,aAAa,QACf;AACE,eAAO,OAAO,CAAC;AAAA,MACnB;AAEA,UAAI,SAAS,UAAa,aAAa,QAAQ,IAAI,IAAI,GAAG;AACtD,qBAAa,KAAK,IAAI;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI,iBAAiB,QAAQ;AACzB,YAAQ,OAAO,KAAK;AAAA,EACxB,WAAW,iBAAiB,QAAQ;AAChC,YAAQ,OAAO,KAAK;AAAA,EACxB;AAEA,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI,QAAQ,GAAG;AACX,cAAQ,KAAK,IAAI,IAAI,KAAK,MAAM,KAAK,CAAC;AACtC,YAAM,aAAa,OAAO,GAAG,KAAK;AAAA,IACtC;AAAA,EACJ,WAAW,OAAO,UAAU,UAAU;AAClC,UAAM,MAAM,OAAO,GAAG,EAAE;AAAA,EAC5B;AAEA,SAAO,kBAAkB,IAAI,EAAC,IAAI,MAAK,CAAC;AAExC,WAAS,kBAAmBJ,MAAK,QAAQ;AACrC,QAAIK,SAAQ,OAAOL,IAAG;AACtB,QAAIK,UAAS,MAAM;AACf,UAAI,OAAOA,OAAM,YAAY,YAAY;AACrC,QAAAA,SAAQA,OAAM,QAAQL,IAAG;AAAA,MAC7B,WAAW,OAAOK,OAAM,WAAW,YAAY;AAC3C,QAAAA,SAAQA,OAAM,OAAOL,IAAG;AAAA,MAC5B;AAAA,IACJ;AAEA,QAAI,cAAc;AACd,MAAAK,SAAQ,aAAa,KAAK,QAAQL,MAAKK,MAAK;AAAA,IAChD;AAEA,QAAIA,kBAAiB,QAAQ;AACzB,MAAAA,SAAQ,OAAOA,MAAK;AAAA,IACxB,WAAWA,kBAAiB,QAAQ;AAChC,MAAAA,SAAQ,OAAOA,MAAK;AAAA,IACxB,WAAWA,kBAAiB,SAAS;AACjC,MAAAA,SAAQA,OAAM,QAAQ;AAAA,IAC1B;AAEA,YAAQA,QAAO;AAAA,MACf,KAAK;AAAM,eAAO;AAAA,MAClB,KAAK;AAAM,eAAO;AAAA,MAClB,KAAK;AAAO,eAAO;AAAA,IACnB;AAEA,QAAI,OAAOA,WAAU,UAAU;AAC3B,aAAO,YAAYA,QAAO,KAAK;AAAA,IACnC;AAEA,QAAI,OAAOA,WAAU,UAAU;AAC3B,aAAO,OAAOA,MAAK;AAAA,IACvB;AAEA,QAAI,OAAOA,WAAU,UAAU;AAC3B,aAAO,MAAM,QAAQA,MAAK,IAAI,eAAeA,MAAK,IAAI,gBAAgBA,MAAK;AAAA,IAC/E;AAEA,WAAO;AAAA,EACX;AAEA,WAAS,YAAaA,QAAO;AACzB,UAAM,SAAS;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AAAA,IACT;AAEA,UAAM,eAAe;AAAA,MACjB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV,UAAU;AAAA,IACd;AAEA,QAAI,UAAU;AAEd,aAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACnC,YAAMP,KAAIO,OAAM,CAAC;AACjB,cAAQP,IAAG;AAAA,QACX,KAAK;AAAA,QACL,KAAK;AACD,iBAAOA,EAAC;AACR,qBAAWA;AACX;AAAA,QAEJ,KAAK;AACD,cAAI,KAAK,QAAQO,OAAM,IAAI,CAAC,CAAC,GAAG;AAC5B,uBAAW;AACX;AAAA,UACJ;AAAA,MACJ;AAEA,UAAI,aAAaP,EAAC,GAAG;AACjB,mBAAW,aAAaA,EAAC;AACzB;AAAA,MACJ;AAEA,UAAIA,KAAI,KAAK;AACT,YAAI,YAAYA,GAAE,WAAW,CAAC,EAAE,SAAS,EAAE;AAC3C,mBAAW,SAAS,OAAO,WAAW,UAAU,UAAU,MAAM;AAChE;AAAA,MACJ;AAEA,iBAAWA;AAAA,IACf;AAEA,UAAM,YAAY,SAAS,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,GAAG,MAAO,OAAO,CAAC,IAAI,OAAO,CAAC,IAAK,IAAI,CAAC;AAE/F,cAAU,QAAQ,QAAQ,IAAI,OAAO,WAAW,GAAG,GAAG,aAAa,SAAS,CAAC;AAE7E,WAAO,YAAY,UAAU;AAAA,EACjC;AAEA,WAAS,gBAAiBO,QAAO;AAC7B,QAAID,OAAM,QAAQC,MAAK,KAAK,GAAG;AAC3B,YAAM,UAAU,wCAAwC;AAAA,IAC5D;AAEA,IAAAD,OAAM,KAAKC,MAAK;AAEhB,QAAI,WAAW;AACf,aAAS,SAAS;AAElB,QAAI,OAAO,gBAAgB,OAAO,KAAKA,MAAK;AAC5C,QAAI,UAAU,CAAC;AACf,eAAWL,QAAO,MAAM;AACpB,YAAM,iBAAiB,kBAAkBA,MAAKK,MAAK;AACnD,UAAI,mBAAmB,QAAW;AAC9B,YAAI,SAAS,aAAaL,IAAG,IAAI;AACjC,YAAI,QAAQ,IAAI;AACZ,oBAAU;AAAA,QACd;AACA,kBAAU;AACV,gBAAQ,KAAK,MAAM;AAAA,MACvB;AAAA,IACJ;AAEA,QAAI;AACJ,QAAI,QAAQ,WAAW,GAAG;AACtB,cAAQ;AAAA,IACZ,OAAO;AACH,UAAI;AACJ,UAAI,QAAQ,IAAI;AACZ,qBAAa,QAAQ,KAAK,GAAG;AAC7B,gBAAQ,MAAM,aAAa;AAAA,MAC/B,OAAO;AACH,YAAI,YAAY,QAAQ;AACxB,qBAAa,QAAQ,KAAK,SAAS;AACnC,gBAAQ,QAAQ,SAAS,aAAa,QAAQ,WAAW;AAAA,MAC7D;AAAA,IACJ;AAEA,IAAAI,OAAM,IAAI;AACV,aAAS;AACT,WAAO;AAAA,EACX;AAEA,WAAS,aAAcJ,MAAK;AACxB,QAAIA,KAAI,WAAW,GAAG;AAClB,aAAO,YAAYA,MAAK,IAAI;AAAA,IAChC;AAEA,UAAM,YAAY,OAAO,cAAcA,KAAI,YAAY,CAAC,CAAC;AACzD,QAAI,CAAC,KAAK,cAAc,SAAS,GAAG;AAChC,aAAO,YAAYA,MAAK,IAAI;AAAA,IAChC;AAEA,aAAS,IAAI,UAAU,QAAQ,IAAIA,KAAI,QAAQ,KAAK;AAChD,UAAI,CAAC,KAAK,iBAAiB,OAAO,cAAcA,KAAI,YAAY,CAAC,CAAC,CAAC,GAAG;AAClE,eAAO,YAAYA,MAAK,IAAI;AAAA,MAChC;AAAA,IACJ;AAEA,WAAOA;AAAA,EACX;AAEA,WAAS,eAAgBK,QAAO;AAC5B,QAAID,OAAM,QAAQC,MAAK,KAAK,GAAG;AAC3B,YAAM,UAAU,wCAAwC;AAAA,IAC5D;AAEA,IAAAD,OAAM,KAAKC,MAAK;AAEhB,QAAI,WAAW;AACf,aAAS,SAAS;AAElB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACnC,YAAM,iBAAiB,kBAAkB,OAAO,CAAC,GAAGA,MAAK;AACzD,cAAQ,KAAM,mBAAmB,SAAa,iBAAiB,MAAM;AAAA,IACzE;AAEA,QAAI;AACJ,QAAI,QAAQ,WAAW,GAAG;AACtB,cAAQ;AAAA,IACZ,OAAO;AACH,UAAI,QAAQ,IAAI;AACZ,YAAI,aAAa,QAAQ,KAAK,GAAG;AACjC,gBAAQ,MAAM,aAAa;AAAA,MAC/B,OAAO;AACH,YAAI,YAAY,QAAQ;AACxB,YAAI,aAAa,QAAQ,KAAK,SAAS;AACvC,gBAAQ,QAAQ,SAAS,aAAa,QAAQ,WAAW;AAAA,MAC7D;AAAA,IACJ;AAEA,IAAAD,OAAM,IAAI;AACV,aAAS;AACT,WAAO;AAAA,EACX;AACJ;AAEA,IAAM,QAAQ;AAAA,EACV;AAAA,EACA;AACJ;AAEA,IAAI,MAAM;AAEV,IAAO,eAAQ;", "names": ["c", "parse", "key", "token", "buffer", "stringify", "stack", "value"]}