"""
Psychiatry EMR - Dashboard Page
Main navigation, patient overview, and system status.
"""

import reflex as rx
from datetime import datetime

from states.auth_state import AuthState
from states.patient_state import PatientState
from states.clinical_state import ClinicalState

def dashboard_page() -> rx.Component:
    """Main dashboard page with navigation and overview"""
    return rx.vstack(
        # Header with navigation
        dashboard_header(),
        
        # Main content area
        rx.container(
            rx.vstack(
                # Welcome section
                welcome_section(),
                
                # Quick stats cards
                stats_overview_section(),
                
                # Recent activity and quick actions
                rx.grid(
                    # Recent patients section
                    recent_patients_section(),
                    
                    # Quick actions section
                    quick_actions_section(),
                    
                    columns="2",
                    spacing="6",
                    width="100%"
                ),
                
                # System status section
                system_status_section(),
                
                spacing=6,
                width="100%"
            ),
            max_width="1200px",
            p=6
        ),
        
        width="100%",
        min_height="100vh",
        bg="gray.50"
    )

def dashboard_header() -> rx.Component:
    """Dashboard header with navigation and user info"""
    return rx.box(
        rx.container(
            rx.hstack(
                # Logo and title
                rx.hstack(
                    rx.icon("brain-circuit", size=32, color="blue.600"),
                    rx.heading("Psychiatry EMR", size="lg", color="blue.600"),
                    spacing=3
                ),
                
                # Navigation menu
                rx.hstack(
                    rx.link("Dashboard", href="/dashboard", font_weight="bold"),
                    rx.link("Patients", href="/patients"),
                    rx.link("Assessments", href="/assessments"),
                    rx.link("Reports", href="/reports"),
                    spacing=6
                ),
                
                rx.spacer(),
                
                # User menu
                rx.hstack(
                    rx.text(f"Welcome, {AuthState.current_user.full_name}", font_weight="medium"),
                    rx.menu.root(
                        rx.menu.trigger(
                            rx.button(
                                rx.icon("user", size=20),
                                variant="ghost",
                                size="sm"
                            )
                        ),
                        rx.menu.content(
                            rx.menu.item("Profile Settings"),
                            rx.menu.item("Change Password"),
                            rx.menu.separator(),
                            rx.menu.item(
                                "Logout",
                                on_click=AuthState.logout,
                                color="red.600"
                            )
                        )
                    ),
                    spacing=3
                ),
                
                width="100%",
                align="center"
            ),
            max_width="1200px"
        ),
        width="100%",
        bg="white",
        border_bottom="1px solid",
        border_color="gray.200",
        p=4
    )

def welcome_section() -> rx.Component:
    """Welcome section with current date and quick info"""
    return rx.card(
        rx.vstack(
            rx.hstack(
                rx.vstack(
                    rx.heading(f"Good {get_time_of_day()}, {AuthState.current_user.full_name}!", size="lg"),
                    rx.text(f"Today is {datetime.now().strftime('%A, %B %d, %Y')}", color="gray.600"),
                    spacing=1,
                    align="start"
                ),
                rx.spacer(),
                rx.vstack(
                    rx.text("Your Role", font_size="sm", color="gray.500"),
                    rx.badge(AuthState.current_user.role.title(), color_scheme="blue", size="lg"),
                    spacing=1,
                    align="end"
                ),
                width="100%",
                align="center"
            ),
            spacing=3
        ),
        width="100%",
        p=6,
        bg="gradient-to-r from-blue-50 to-purple-50"
    )

def stats_overview_section() -> rx.Component:
    """Overview statistics cards"""
    return rx.grid(
        # Total patients card
        stat_card(
            title="Total Patients",
            value="1,247",
            change="+12 this month",
            icon="users",
            color="blue"
        ),
        
        # Active assessments card
        stat_card(
            title="Active Assessments",
            value="89",
            change="+5 today",
            icon="clipboard-list",
            color="green"
        ),
        
        # Pending reviews card
        stat_card(
            title="Pending Reviews",
            value="23",
            change="-3 from yesterday",
            icon="clock",
            color="yellow"
        ),
        
        # This month's assessments
        stat_card(
            title="This Month",
            value="156",
            change="Assessments completed",
            icon="trending-up",
            color="purple"
        ),
        
        columns="4",
        spacing="4",
        width="100%"
    )

def stat_card(title: str, value: str, change: str, icon: str, color: str) -> rx.Component:
    """Individual statistics card"""
    return rx.card(
        rx.vstack(
            rx.hstack(
                rx.vstack(
                    rx.text(title, font_size="sm", color="gray.600"),
                    rx.heading(value, size="lg"),
                    spacing=1,
                    align="start"
                ),
                rx.spacer(),
                rx.icon(icon, size=24, color=f"{color}.500"),
                width="100%",
                align="start"
            ),
            rx.text(change, font_size="sm", color=f"{color}.600"),
            spacing=3,
            align="start"
        ),
        p=4,
        width="100%"
    )

def recent_patients_section() -> rx.Component:
    """Recent patients section"""
    return rx.card(
        rx.vstack(
            rx.hstack(
                rx.heading("Recent Patients", size="md"),
                rx.spacer(),
                rx.link("View All", href="/patients", color="blue.600"),
                width="100%"
            ),
            
            # Recent patients list (placeholder data)
            rx.vstack(
                patient_list_item("Sarah Johnson", "2024-01-15", "Follow-up scheduled"),
                patient_list_item("Michael Chen", "2024-01-14", "Assessment completed"),
                patient_list_item("Emily Davis", "2024-01-13", "New patient intake"),
                patient_list_item("Robert Wilson", "2024-01-12", "Treatment plan updated"),
                patient_list_item("Lisa Anderson", "2024-01-11", "Medication review"),
                spacing=2,
                width="100%"
            ),
            
            spacing=4,
            align="start"
        ),
        p=4,
        width="100%"
    )

def patient_list_item(name: str, date: str, status: str) -> rx.Component:
    """Individual patient list item"""
    return rx.hstack(
        rx.vstack(
            rx.text(name, font_weight="medium"),
            rx.text(status, font_size="sm", color="gray.600"),
            spacing=1,
            align="start"
        ),
        rx.spacer(),
        rx.text(date, font_size="sm", color="gray.500"),
        width="100%",
        p=2,
        border_radius="md",
        _hover={"bg": "gray.50"}
    )

def quick_actions_section() -> rx.Component:
    """Quick actions section"""
    return rx.card(
        rx.vstack(
            rx.heading("Quick Actions", size="md", mb=4),
            
            rx.vstack(
                rx.button(
                    rx.hstack(
                        rx.icon("user-plus", size=20),
                        rx.text("New Patient"),
                        spacing=2
                    ),
                    width="100%",
                    color_scheme="blue",
                    size="lg",
                    justify="start"
                ),
                
                rx.button(
                    rx.hstack(
                        rx.icon("clipboard-plus", size=20),
                        rx.text("New Assessment"),
                        spacing=2
                    ),
                    width="100%",
                    color_scheme="green",
                    size="lg",
                    justify="start"
                ),
                
                rx.button(
                    rx.hstack(
                        rx.icon("search", size=20),
                        rx.text("Search Patients"),
                        spacing=2
                    ),
                    width="100%",
                    color_scheme="purple",
                    size="lg",
                    justify="start"
                ),
                
                rx.button(
                    rx.hstack(
                        rx.icon("bar-chart", size=20),
                        rx.text("View Reports"),
                        spacing=2
                    ),
                    width="100%",
                    color_scheme="orange",
                    size="lg",
                    justify="start"
                ),
                
                spacing=3,
                width="100%"
            ),
            
            spacing=4,
            align="start"
        ),
        p=4,
        width="100%"
    )

def system_status_section() -> rx.Component:
    """System status and health indicators"""
    return rx.card(
        rx.vstack(
            rx.heading("System Status", size="md", mb=4),
            
            rx.grid(
                status_indicator("Database", "Connected", "green"),
                status_indicator("Encryption", "Active", "green"),
                status_indicator("Backup", "Last: 2 hours ago", "green"),
                status_indicator("Security", "All systems normal", "green"),
                
                columns="4",
                spacing="4",
                width="100%"
            ),
            
            spacing=4,
            align="start"
        ),
        p=4,
        width="100%",
        bg="gray.50"
    )

def status_indicator(label: str, status: str, color: str) -> rx.Component:
    """Individual status indicator"""
    return rx.hstack(
        rx.icon("circle", size=12, color=f"{color}.500"),
        rx.vstack(
            rx.text(label, font_weight="medium", font_size="sm"),
            rx.text(status, font_size="xs", color="gray.600"),
            spacing=0,
            align="start"
        ),
        spacing=2,
        align="center"
    )

def get_time_of_day() -> str:
    """Get appropriate greeting based on time of day"""
    hour = datetime.now().hour
    if hour < 12:
        return "morning"
    elif hour < 17:
        return "afternoon"
    else:
        return "evening"
