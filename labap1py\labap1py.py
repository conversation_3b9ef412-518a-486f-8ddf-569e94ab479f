"""
Psychiatry EMR - Main Application
Production-ready Reflex application for psychiatry practice management.
"""

import reflex as rx
import logging
import os
import getpass
from pathlib import Path
import sys

# Import all the necessary components from the project
from security.encryption import initialize_encryption
from services.database import get_engine
from config.settings import get_settings
from pages.dashboard import dashboard_page
from pages.patient_search import patient_search_page
from pages.clinical_assessment import clinical_assessment_page
from components.navigation import main_layout, protected_route
from states.auth_state import AuthState

# Setup logging
def setup_logging():
    """Configure comprehensive logging"""
    try:
        settings = get_settings()
        logging.basicConfig(
            level=getattr(logging, settings.log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            handlers=[
                logging.FileHandler(settings.log_file),
                logging.StreamHandler()
            ]
        )
    except Exception as e:
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )
        print(f"Warning: Could not load settings for logging: {e}")

# Initialize database
def initialize_database():
    """Initialize database schema"""
    logger = logging.getLogger(__name__)
    
    try:
        # Import all models to ensure they're registered
        from models.patient import Patient, UserPatientAccess
        from models.clinical import PresentIllness
        from models.audit import AuditLog
        from models.user import User, UserSession
        
        engine = get_engine()
        rx.Model.metadata.create_all(engine)
        logger.info("Database tables created/verified")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise

# Get master password
def get_master_password() -> str:
    """Get master password for encryption"""
    if os.getenv("MASTER_PASSWORD"):
        print("⚠️  WARNING: Using master password from environment variable")
        return os.getenv("MASTER_PASSWORD")

    try:
        settings = get_settings()
        print(f"\n🏥 {settings.app_name} v{settings.app_version}")
    except:
        print(f"\n🏥 Psychiatry EMR v1.0.0")

    print("=" * 50)
    
    while True:
        password = getpass.getpass("🔐 Enter master password for encryption: ")
        if len(password) < 8:
            print("❌ Password must be at least 8 characters long")
            continue
            
        confirm = getpass.getpass("🔐 Confirm master password: ")
        if password != confirm:
            print("❌ Passwords do not match")
            continue
            
        return password

# Initialize application
def initialize_application():
    """Initialize application"""
    logger = logging.getLogger(__name__)
    logger.info("Initializing Psychiatry EMR application...")
    
    try:
        # Initialize encryption
        master_password = get_master_password()
        crypto_service = initialize_encryption(master_password)
        logger.info("Encryption service initialized")
        
        # Initialize database
        initialize_database()
        logger.info("Database initialized")
        
        print("🚀 Application initialization complete!")
        
    except KeyboardInterrupt:
        print("\n❌ Initialization cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Application initialization failed: {e}")
        print(f"❌ Initialization failed: {e}")
        sys.exit(1)

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Initialize application components
if not os.getenv("SKIP_INIT"):
    initialize_application()

# Create Reflex app
try:
    settings = get_settings()
    
    app = rx.App(
        style={
            "font_family": "Inter, system-ui, sans-serif",
            "background_color": "#f8fafc",
            "color": "#1e293b",
        },
        stylesheets=[
            "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
        ],
        theme=rx.theme(
            appearance="light",
            has_background=True,
            radius="medium",
            accent_color="blue",
        ),
    )
    
except Exception as e:
    logger.warning(f"Could not load settings for app configuration: {e}")
    app = rx.App()

# Login page
def login_page() -> rx.Component:
    """Login page for authentication"""
    return rx.center(
        rx.card(
            rx.vstack(
                rx.heading("Psychiatry EMR", size="xl", color="blue.600", mb=6),
                rx.text("Secure Patient Management System", color="gray.600", mb=4),
                rx.vstack(
                    rx.input(
                        placeholder="Username",
                        value=AuthState.login_username,
                        on_change=lambda value: AuthState.update_login_form("username", value),
                        width="100%"
                    ),
                    rx.input(
                        placeholder="Password",
                        type="password",
                        value=AuthState.login_password,
                        on_change=lambda value: AuthState.update_login_form("password", value),
                        width="100%"
                    ),
                    rx.button(
                        "Login",
                        on_click=AuthState.login,
                        loading=AuthState.is_loading,
                        color_scheme="blue",
                        width="100%",
                        size="lg"
                    ),
                    spacing=4,
                    width="100%"
                ),
                rx.cond(
                    AuthState.error_message,
                    rx.alert(
                        rx.alert.icon(),
                        rx.alert.description(AuthState.error_message),
                        status="error",
                        width="100%"
                    )
                ),
                rx.cond(
                    AuthState.success_message,
                    rx.alert(
                        rx.alert.icon(),
                        rx.alert.description(AuthState.success_message),
                        status="success",
                        width="100%"
                    )
                ),
                spacing=4,
                width="100%"
            ),
            max_width="400px",
            p=8
        ),
        min_height="100vh",
        bg="gray.50"
    )

# Health check endpoint
@rx.page(route="/health", title="Health Check")
def health_check():
    """Health check endpoint"""
    try:
        from services.database import get_engine
        engine = get_engine()
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        return rx.text("Health check: OK - All services running")
    except Exception as e:
        return rx.text(f"Health check: ERROR - {str(e)}")

# Main pages
@rx.page(route="/", title="Psychiatry EMR")
def index():
    """Welcome page"""
    return rx.container(
        rx.vstack(
            rx.heading("🏥 Psychiatry EMR", size="9"),
            rx.text("Secure Patient Management System", size="5", color="gray"),
            rx.divider(),
            rx.text("✅ Application successfully initialized!", size="4", color="green"),
            rx.text("🔐 Encryption service active", size="3"),
            rx.text("🗄️ Database connected", size="3"),
            rx.text("📊 Ready for patient management", size="3"),
            rx.divider(),
            rx.hstack(
                rx.button("Go to Dashboard", on_click=rx.redirect("/dashboard"), color_scheme="blue"),
                rx.button("Patient Search", on_click=rx.redirect("/patients"), color_scheme="green"),
                spacing="4"
            ),
            spacing="4",
            align="center",
            min_height="100vh",
            justify="center",
        ),
        max_width="800px",
        margin="0 auto",
        padding="2rem",
    )

@rx.page(route="/dashboard", title="Dashboard - Psychiatry EMR")
def dashboard():
    """Dashboard page"""
    return protected_route(main_layout(dashboard_page()))

@rx.page(route="/patients", title="Patient Search - Psychiatry EMR")
def patients():
    """Patient search page"""
    return protected_route(main_layout(patient_search_page()))

@rx.page(route="/assessments", title="Clinical Assessment - Psychiatry EMR")
def assessments():
    """Clinical assessment page"""
    return protected_route(main_layout(clinical_assessment_page()))

@rx.page(route="/login", title="Login - Psychiatry EMR")
def login():
    """Login page"""
    return login_page()
