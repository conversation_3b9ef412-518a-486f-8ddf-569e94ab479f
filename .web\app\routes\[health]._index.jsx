

import { Fragment, useEffect } from "react"
import { Text as RadixThemesText } from "@radix-ui/themes"
import { jsx } from "@emotion/react"



export default function Component() {
    




  return (
    jsx(
Fragment,
{},
jsx(
RadixThemesText,
{as:"p"},
"Health check: ERROR - Not an executable object: 'SELECT 1'"
,),jsx(
"title",
{},
"Health Check"
,),jsx("meta",{content:"favicon.ico",property:"og:image"},)
,)
  )
}
