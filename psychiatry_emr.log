2025-07-28 16:28:24,456 - main - INFO - initialize_application:132 - Initializing Psychiatry EMR v1.0.0
2025-07-28 16:28:24,472 - main - INFO - check_environment:119 - Environment configuration validated
2025-07-28 16:29:29,408 - security.encryption - INFO - initialize_encryption:83 - Encryption service initialized
2025-07-28 16:29:31,003 - services.database - INFO - get_engine:31 - Database engine initialized
2025-07-28 16:29:31,158 - main - ERROR - initialize_database:67 - Database initialization failed: email-validator is not installed, run `pip install pydantic[email]`
2025-07-28 16:29:31,160 - main - ERROR - initialize_application:162 - Application initialization failed: email-validator is not installed, run `pip install pydantic[email]`
2025-07-28 16:31:11,378 - main - INFO - initialize_application:132 - Initializing Psychiatry EMR v1.0.0
2025-07-28 16:31:11,383 - main - INFO - check_environment:119 - Environment configuration validated
2025-07-28 16:32:09,621 - security.encryption - INFO - initialize_encryption:83 - Encryption service initialized
2025-07-28 16:32:09,774 - services.database - INFO - get_engine:31 - Database engine initialized
2025-07-28 16:32:13,721 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-28 16:32:13,736 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-28 16:32:13,737 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,748 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("patient")
2025-07-28 16:32:13,756 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,762 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-28 16:32:13,772 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,782 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("userpatientaccess")
2025-07-28 16:32:13,786 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,789 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-28 16:32:13,798 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,801 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("presentillness")
2025-07-28 16:32:13,804 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,806 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-28 16:32:13,817 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,820 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("auditlog")
2025-07-28 16:32:13,821 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,822 - sqlalchemy.engine.Engine - INFO - _connection_rollback_impl:2701 - ROLLBACK
2025-07-28 16:32:13,823 - main - ERROR - initialize_database:67 - Database initialization failed: Foreign key associated with column 'patient.updated_by' could not find table 'user' with which to generate a foreign key to target column 'id'
2025-07-28 16:32:13,831 - main - ERROR - initialize_application:162 - Application initialization failed: Foreign key associated with column 'patient.updated_by' could not find table 'user' with which to generate a foreign key to target column 'id'
2025-07-28 16:34:12,253 - main - INFO - initialize_application:132 - Initializing Psychiatry EMR v1.0.0
2025-07-28 16:34:12,267 - main - INFO - check_environment:119 - Environment configuration validated
2025-07-28 16:35:22,550 - security.encryption - INFO - initialize_encryption:83 - Encryption service initialized
2025-07-28 16:35:22,627 - services.database - INFO - get_engine:31 - Database engine initialized
2025-07-28 16:35:23,319 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-28 16:35:23,320 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-28 16:35:23,321 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,328 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("patient")
2025-07-28 16:35:23,332 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,334 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-28 16:35:23,385 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,401 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("userpatientaccess")
2025-07-28 16:35:23,402 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,403 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-28 16:35:23,404 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,405 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("presentillness")
2025-07-28 16:35:23,433 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,453 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-28 16:35:23,464 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,466 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("auditlog")
2025-07-28 16:35:23,470 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,485 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE patient (
	id INTEGER NOT NULL, 
	name_encrypted VARCHAR NOT NULL, 
	dob DATE NOT NULL, 
	address_encrypted VARCHAR, 
	phone_encrypted VARCHAR, 
	email_encrypted VARCHAR, 
	education VARCHAR, 
	occupation VARCHAR, 
	living_situation VARCHAR, 
	created_at DATETIME NOT NULL, 
	updated_by INTEGER, 
	is_active BOOLEAN NOT NULL, 
	merged_into INTEGER, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_patient_name_dob UNIQUE (name_encrypted, dob), 
	FOREIGN KEY(merged_into) REFERENCES patient (id)
)


2025-07-28 16:35:23,550 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.06527s] ()
2025-07-28 16:35:23,694 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE INDEX ix_patient_created_at ON patient (created_at)
2025-07-28 16:35:23,697 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.00311s] ()
2025-07-28 16:35:23,788 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE INDEX ix_patient_dob ON patient (dob)
2025-07-28 16:35:23,794 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.00600s] ()
2025-07-28 16:35:23,916 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE auditlog (
	id INTEGER NOT NULL, 
	user_id INTEGER NOT NULL, 
	timestamp DATETIME NOT NULL, 
	action VARCHAR NOT NULL, 
	table_name VARCHAR NOT NULL, 
	record_id INTEGER, 
	old_values VARCHAR, 
	new_values VARCHAR, 
	ip_address VARCHAR, 
	user_agent VARCHAR, 
	session_id VARCHAR, 
	success BOOLEAN NOT NULL, 
	error_message VARCHAR, 
	PRIMARY KEY (id)
)


2025-07-28 16:35:23,936 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.02083s] ()
2025-07-28 16:35:24,046 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE userpatientaccess (
	id INTEGER NOT NULL, 
	user_id INTEGER NOT NULL, 
	patient_id INTEGER NOT NULL, 
	granted_at DATETIME NOT NULL, 
	granted_by INTEGER NOT NULL, 
	is_active BOOLEAN NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(patient_id) REFERENCES patient (id)
)


2025-07-28 16:35:24,050 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.00466s] ()
2025-07-28 16:35:24,154 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE presentillness (
	id INTEGER NOT NULL, 
	patient_id INTEGER NOT NULL, 
	assessment_date DATE NOT NULL, 
	chief_complaint VARCHAR NOT NULL, 
	history_present_illness VARCHAR NOT NULL, 
	primary_diagnosis VARCHAR, 
	secondary_diagnoses VARCHAR, 
	dsm5_criteria_json VARCHAR, 
	treatment_plan VARCHAR, 
	created_at DATETIME NOT NULL, 
	updated_by INTEGER NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(patient_id) REFERENCES patient (id)
)


2025-07-28 16:35:24,203 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.04871s] ()
2025-07-28 16:35:24,321 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE INDEX ix_presentillness_assessment_date ON presentillness (assessment_date)
2025-07-28 16:35:24,332 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.01143s] ()
2025-07-28 16:35:24,447 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-28 16:35:24,451 - main - INFO - initialize_database:57 - Database tables created/verified
2025-07-28 16:35:24,465 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-28 16:35:24,471 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
-- Performance indexes for common queries
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);

CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id) WHERE is_active = true;

-- Partial index for active patients only (more efficient)
CREATE INDEX IF NOT EXISTS idx_patient_active_only ON patient(id) WHERE is_active = true;

2025-07-28 16:35:24,502 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [generated in 0.03681s] ()
2025-07-28 16:35:24,503 - sqlalchemy.engine.Engine - INFO - _connection_rollback_impl:2701 - ROLLBACK
2025-07-28 16:35:24,504 - main - ERROR - initialize_database:67 - Database initialization failed: (sqlite3.ProgrammingError) You can only execute one statement at a time.
[SQL: 
-- Performance indexes for common queries
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);

CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id) WHERE is_active = true;

-- Partial index for active patients only (more efficient)
CREATE INDEX IF NOT EXISTS idx_patient_active_only ON patient(id) WHERE is_active = true;
]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-28 16:35:24,519 - main - ERROR - initialize_application:162 - Application initialization failed: (sqlite3.ProgrammingError) You can only execute one statement at a time.
[SQL: 
-- Performance indexes for common queries
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);

CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id) WHERE is_active = true;

-- Partial index for active patients only (more efficient)
CREATE INDEX IF NOT EXISTS idx_patient_active_only ON patient(id) WHERE is_active = true;
]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-28 16:36:37,521 - main - INFO - initialize_application:136 - Initializing Psychiatry EMR v1.0.0
2025-07-28 16:36:37,522 - main - INFO - check_environment:123 - Environment configuration validated
2025-07-28 16:37:42,820 - security.encryption - INFO - initialize_encryption:83 - Encryption service initialized
2025-07-28 16:37:42,907 - services.database - INFO - get_engine:31 - Database engine initialized
2025-07-28 16:37:43,336 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-28 16:37:43,337 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-28 16:37:43,339 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:37:43,390 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-28 16:37:43,393 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:37:43,405 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-28 16:37:43,407 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:37:43,409 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-28 16:37:43,422 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:37:43,423 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-28 16:37:43,424 - main - INFO - initialize_database:57 - Database tables created/verified
2025-07-28 16:37:43,424 - main - INFO - initialize_database:68 - Skipping performance indexes for SQLite
2025-07-28 16:37:53,955 - main - WARNING - <module>:203 - Could not load settings for app configuration: App.__init__() got an unexpected keyword argument 'state'. Did you mean '_state'?
2025-07-28 16:39:32,585 - main - INFO - initialize_application:136 - Initializing Psychiatry EMR v1.0.0
2025-07-28 16:39:32,586 - main - INFO - check_environment:123 - Environment configuration validated
2025-07-29 00:05:57,063 - main - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 00:05:57,121 - main - INFO - check_environment:130 - Environment configuration validated
2025-07-29 00:23:09,528 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 00:23:09,529 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 00:24:30,603 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 00:24:30,604 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 01:30:04,873 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 01:30:04,904 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 01:32:12,597 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 01:32:12,599 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 01:32:12,817 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:32:14,277 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:32:14,297 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:32:14,301 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:32:14,303 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,320 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("user")
2025-07-29 01:32:14,333 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,336 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:32:14,341 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,345 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("usersession")
2025-07-29 01:32:14,359 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,366 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:32:14,385 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,403 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:32:14,420 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,469 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:32:14,501 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,503 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:32:14,553 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,601 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE user (
	id INTEGER NOT NULL, 
	username VARCHAR(50) NOT NULL, 
	password_hash VARCHAR(255) NOT NULL, 
	full_name VARCHAR(100) NOT NULL, 
	email VARCHAR(100), 
	role VARCHAR(20) NOT NULL, 
	is_active BOOLEAN NOT NULL, 
	is_verified BOOLEAN NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME, 
	last_login DATETIME, 
	password_changed_at DATETIME, 
	PRIMARY KEY (id)
)


2025-07-29 01:32:14,680 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.07914s] ()
2025-07-29 01:32:14,929 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE UNIQUE INDEX ix_user_username ON user (username)
2025-07-29 01:32:15,113 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.18370s] ()
2025-07-29 01:32:15,461 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE usersession (
	id INTEGER NOT NULL, 
	user_id INTEGER NOT NULL, 
	session_token VARCHAR(255) NOT NULL, 
	ip_address VARCHAR(45), 
	user_agent VARCHAR(500), 
	is_active BOOLEAN NOT NULL, 
	created_at DATETIME NOT NULL, 
	last_activity DATETIME NOT NULL, 
	expires_at DATETIME NOT NULL, 
	logged_out_at DATETIME, 
	PRIMARY KEY (id), 
	FOREIGN KEY(user_id) REFERENCES user (id)
)


2025-07-29 01:32:15,493 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.03149s] ()
2025-07-29 01:32:15,735 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE INDEX ix_usersession_user_id ON usersession (user_id)
2025-07-29 01:32:15,739 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.00463s] ()
2025-07-29 01:32:16,004 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE UNIQUE INDEX ix_usersession_session_token ON usersession (session_token)
2025-07-29 01:32:16,019 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.01526s] ()
2025-07-29 01:32:16,235 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:32:16,239 - __main__ - INFO - initialize_database:64 - Database tables created/verified
2025-07-29 01:32:16,242 - __main__ - INFO - initialize_database:75 - Skipping performance indexes for SQLite
2025-07-29 01:32:24,972 - __main__ - INFO - <module>:362 - Starting Psychiatry EMR application...
2025-07-29 01:32:24,973 - __main__ - ERROR - <module>:369 - Failed to start application: 'App' object has no attribute 'run'
2025-07-29 01:32:54,616 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 01:32:54,617 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 01:32:54,812 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:32:54,915 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:32:54,975 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:32:55,095 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:32:55,122 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:55,317 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:32:55,431 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:55,559 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:32:55,865 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:55,941 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:32:56,068 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:56,102 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:32:56,128 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:56,265 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:32:56,317 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:56,441 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:32:56,499 - __main__ - INFO - initialize_database:64 - Database tables created/verified
2025-07-29 01:32:56,564 - __main__ - INFO - initialize_database:75 - Skipping performance indexes for SQLite
2025-07-29 01:33:01,691 - __main__ - INFO - <module>:362 - Starting Psychiatry EMR application...
2025-07-29 01:33:01,692 - __main__ - ERROR - <module>:369 - Failed to start application: 'App' object has no attribute 'run'
2025-07-29 01:48:00,828 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 01:48:02,696 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:48:02,698 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 01:48:03,205 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:48:03,443 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:48:03,465 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:48:03,467 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,491 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:48:03,495 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,506 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:48:03,509 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,511 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:48:03,513 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,515 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:48:03,516 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,520 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:48:03,522 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,523 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:48:03,524 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 01:48:03,524 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 01:48:12,239 - concurrent.futures - ERROR - _invoke_callbacks:342 - exception calling callback for <Future at 0x1f5dce334d0 state=finished raised AttributeError>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\sqlalchemy\engine\base.py", line 1411, in execute
    meth = statement._execute_on_connection
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Text' object has no attribute '_execute_on_connection'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\projects\labap1py\labap1py\labap1py.py", line 220, in health_check
    conn.execute(rx.text("SELECT 1"))
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\sqlalchemy\engine\base.py", line 1413, in execute
    raise exc.ObjectNotExecutableError(statement) from err
sqlalchemy.exc.ObjectNotExecutableError: Not an executable object: {"name": "RadixThemesText", "props": ["as:\"p\""], "contents": "", "special_props": [], "children": [{"name": "", "props": [], "contents": "\"SELECT 1\"", "special_props": [], "children": [], "autofocus": false}], "autofocus": false}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Python313\Lib\concurrent\futures\_base.py", line 340, in _invoke_callbacks
    callback(self)
    ~~~~~~~~^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\app.py", line 611, in callback
    return f.result()
           ~~~~~~~~^^
  File "C:\Python313\Lib\concurrent\futures\_base.py", line 449, in result
    return self.__get_result()
           ~~~~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\concurrent\futures\_base.py", line 401, in __get_result
    raise self._exception
  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\app.py", line 1215, in _compile
    self._compile_page(route, save_page=should_compile)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\app.py", line 854, in _compile_page
    component = compiler.compile_unevaluated_page(
        route, self._unevaluated_pages[route], self.style, self.theme
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\compiler\compiler.py", line 800, in compile_unevaluated_page
    component = into_component(page.component)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\compiler\compiler.py", line 728, in into_component
    component_called = component()
  File "C:\Users\<USER>\projects\labap1py\labap1py\labap1py.py", line 230, in health_check
    return rx.json({
           ^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\__init__.py", line 371, in __getattr__
    return getattr(name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\utils\lazy_loader.py", line 88, in __getattr__
    raise AttributeError(msg)
AttributeError: No reflex attribute json
Happened while evaluating page 'health'
2025-07-29 01:49:05,603 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 01:49:05,605 - labap1py.labap1py - ERROR - initialize_application:113 - Application initialization failed: 
2025-07-29 01:51:05,528 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 01:51:05,529 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 01:51:05,631 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:51:05,899 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:51:05,907 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:51:05,909 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:51:05,911 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,916 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:51:05,917 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,918 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:51:05,920 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,923 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:51:05,924 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,926 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:51:05,928 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,932 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:51:05,937 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,939 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:51:05,940 - __main__ - INFO - initialize_database:64 - Database tables created/verified
2025-07-29 01:51:05,941 - __main__ - INFO - initialize_database:75 - Skipping performance indexes for SQLite
2025-07-29 01:51:07,755 - __main__ - INFO - <module>:362 - Starting Psychiatry EMR application...
2025-07-29 01:51:07,756 - __main__ - ERROR - <module>:369 - Failed to start application: 'App' object has no attribute 'run'
