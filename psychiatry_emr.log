2025-07-28 16:28:24,456 - main - INFO - initialize_application:132 - Initializing Psychiatry EMR v1.0.0
2025-07-28 16:28:24,472 - main - INFO - check_environment:119 - Environment configuration validated
2025-07-28 16:29:29,408 - security.encryption - INFO - initialize_encryption:83 - Encryption service initialized
2025-07-28 16:29:31,003 - services.database - INFO - get_engine:31 - Database engine initialized
2025-07-28 16:29:31,158 - main - ERROR - initialize_database:67 - Database initialization failed: email-validator is not installed, run `pip install pydantic[email]`
2025-07-28 16:29:31,160 - main - ERROR - initialize_application:162 - Application initialization failed: email-validator is not installed, run `pip install pydantic[email]`
2025-07-28 16:31:11,378 - main - INFO - initialize_application:132 - Initializing Psychiatry EMR v1.0.0
2025-07-28 16:31:11,383 - main - INFO - check_environment:119 - Environment configuration validated
2025-07-28 16:32:09,621 - security.encryption - INFO - initialize_encryption:83 - Encryption service initialized
2025-07-28 16:32:09,774 - services.database - INFO - get_engine:31 - Database engine initialized
2025-07-28 16:32:13,721 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-28 16:32:13,736 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-28 16:32:13,737 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,748 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("patient")
2025-07-28 16:32:13,756 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,762 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-28 16:32:13,772 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,782 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("userpatientaccess")
2025-07-28 16:32:13,786 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,789 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-28 16:32:13,798 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,801 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("presentillness")
2025-07-28 16:32:13,804 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,806 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-28 16:32:13,817 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,820 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("auditlog")
2025-07-28 16:32:13,821 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,822 - sqlalchemy.engine.Engine - INFO - _connection_rollback_impl:2701 - ROLLBACK
2025-07-28 16:32:13,823 - main - ERROR - initialize_database:67 - Database initialization failed: Foreign key associated with column 'patient.updated_by' could not find table 'user' with which to generate a foreign key to target column 'id'
2025-07-28 16:32:13,831 - main - ERROR - initialize_application:162 - Application initialization failed: Foreign key associated with column 'patient.updated_by' could not find table 'user' with which to generate a foreign key to target column 'id'
2025-07-28 16:34:12,253 - main - INFO - initialize_application:132 - Initializing Psychiatry EMR v1.0.0
2025-07-28 16:34:12,267 - main - INFO - check_environment:119 - Environment configuration validated
2025-07-28 16:35:22,550 - security.encryption - INFO - initialize_encryption:83 - Encryption service initialized
2025-07-28 16:35:22,627 - services.database - INFO - get_engine:31 - Database engine initialized
2025-07-28 16:35:23,319 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-28 16:35:23,320 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-28 16:35:23,321 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,328 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("patient")
2025-07-28 16:35:23,332 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,334 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-28 16:35:23,385 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,401 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("userpatientaccess")
2025-07-28 16:35:23,402 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,403 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-28 16:35:23,404 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,405 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("presentillness")
2025-07-28 16:35:23,433 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,453 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-28 16:35:23,464 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,466 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("auditlog")
2025-07-28 16:35:23,470 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,485 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE patient (
	id INTEGER NOT NULL, 
	name_encrypted VARCHAR NOT NULL, 
	dob DATE NOT NULL, 
	address_encrypted VARCHAR, 
	phone_encrypted VARCHAR, 
	email_encrypted VARCHAR, 
	education VARCHAR, 
	occupation VARCHAR, 
	living_situation VARCHAR, 
	created_at DATETIME NOT NULL, 
	updated_by INTEGER, 
	is_active BOOLEAN NOT NULL, 
	merged_into INTEGER, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_patient_name_dob UNIQUE (name_encrypted, dob), 
	FOREIGN KEY(merged_into) REFERENCES patient (id)
)


2025-07-28 16:35:23,550 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.06527s] ()
2025-07-28 16:35:23,694 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE INDEX ix_patient_created_at ON patient (created_at)
2025-07-28 16:35:23,697 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.00311s] ()
2025-07-28 16:35:23,788 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE INDEX ix_patient_dob ON patient (dob)
2025-07-28 16:35:23,794 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.00600s] ()
2025-07-28 16:35:23,916 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE auditlog (
	id INTEGER NOT NULL, 
	user_id INTEGER NOT NULL, 
	timestamp DATETIME NOT NULL, 
	action VARCHAR NOT NULL, 
	table_name VARCHAR NOT NULL, 
	record_id INTEGER, 
	old_values VARCHAR, 
	new_values VARCHAR, 
	ip_address VARCHAR, 
	user_agent VARCHAR, 
	session_id VARCHAR, 
	success BOOLEAN NOT NULL, 
	error_message VARCHAR, 
	PRIMARY KEY (id)
)


2025-07-28 16:35:23,936 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.02083s] ()
2025-07-28 16:35:24,046 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE userpatientaccess (
	id INTEGER NOT NULL, 
	user_id INTEGER NOT NULL, 
	patient_id INTEGER NOT NULL, 
	granted_at DATETIME NOT NULL, 
	granted_by INTEGER NOT NULL, 
	is_active BOOLEAN NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(patient_id) REFERENCES patient (id)
)


2025-07-28 16:35:24,050 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.00466s] ()
2025-07-28 16:35:24,154 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE presentillness (
	id INTEGER NOT NULL, 
	patient_id INTEGER NOT NULL, 
	assessment_date DATE NOT NULL, 
	chief_complaint VARCHAR NOT NULL, 
	history_present_illness VARCHAR NOT NULL, 
	primary_diagnosis VARCHAR, 
	secondary_diagnoses VARCHAR, 
	dsm5_criteria_json VARCHAR, 
	treatment_plan VARCHAR, 
	created_at DATETIME NOT NULL, 
	updated_by INTEGER NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(patient_id) REFERENCES patient (id)
)


2025-07-28 16:35:24,203 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.04871s] ()
2025-07-28 16:35:24,321 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE INDEX ix_presentillness_assessment_date ON presentillness (assessment_date)
2025-07-28 16:35:24,332 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.01143s] ()
2025-07-28 16:35:24,447 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-28 16:35:24,451 - main - INFO - initialize_database:57 - Database tables created/verified
2025-07-28 16:35:24,465 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-28 16:35:24,471 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
-- Performance indexes for common queries
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);

CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id) WHERE is_active = true;

-- Partial index for active patients only (more efficient)
CREATE INDEX IF NOT EXISTS idx_patient_active_only ON patient(id) WHERE is_active = true;

2025-07-28 16:35:24,502 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [generated in 0.03681s] ()
2025-07-28 16:35:24,503 - sqlalchemy.engine.Engine - INFO - _connection_rollback_impl:2701 - ROLLBACK
2025-07-28 16:35:24,504 - main - ERROR - initialize_database:67 - Database initialization failed: (sqlite3.ProgrammingError) You can only execute one statement at a time.
[SQL: 
-- Performance indexes for common queries
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);

CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id) WHERE is_active = true;

-- Partial index for active patients only (more efficient)
CREATE INDEX IF NOT EXISTS idx_patient_active_only ON patient(id) WHERE is_active = true;
]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-28 16:35:24,519 - main - ERROR - initialize_application:162 - Application initialization failed: (sqlite3.ProgrammingError) You can only execute one statement at a time.
[SQL: 
-- Performance indexes for common queries
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);

CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id) WHERE is_active = true;

-- Partial index for active patients only (more efficient)
CREATE INDEX IF NOT EXISTS idx_patient_active_only ON patient(id) WHERE is_active = true;
]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-28 16:36:37,521 - main - INFO - initialize_application:136 - Initializing Psychiatry EMR v1.0.0
2025-07-28 16:36:37,522 - main - INFO - check_environment:123 - Environment configuration validated
2025-07-28 16:37:42,820 - security.encryption - INFO - initialize_encryption:83 - Encryption service initialized
2025-07-28 16:37:42,907 - services.database - INFO - get_engine:31 - Database engine initialized
2025-07-28 16:37:43,336 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-28 16:37:43,337 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-28 16:37:43,339 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:37:43,390 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-28 16:37:43,393 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:37:43,405 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-28 16:37:43,407 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:37:43,409 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-28 16:37:43,422 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:37:43,423 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-28 16:37:43,424 - main - INFO - initialize_database:57 - Database tables created/verified
2025-07-28 16:37:43,424 - main - INFO - initialize_database:68 - Skipping performance indexes for SQLite
2025-07-28 16:37:53,955 - main - WARNING - <module>:203 - Could not load settings for app configuration: App.__init__() got an unexpected keyword argument 'state'. Did you mean '_state'?
2025-07-28 16:39:32,585 - main - INFO - initialize_application:136 - Initializing Psychiatry EMR v1.0.0
2025-07-28 16:39:32,586 - main - INFO - check_environment:123 - Environment configuration validated
2025-07-29 00:05:57,063 - main - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 00:05:57,121 - main - INFO - check_environment:130 - Environment configuration validated
2025-07-29 00:23:09,528 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 00:23:09,529 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 00:24:30,603 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 00:24:30,604 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 01:30:04,873 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 01:30:04,904 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 01:32:12,597 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 01:32:12,599 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 01:32:12,817 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:32:14,277 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:32:14,297 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:32:14,301 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:32:14,303 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,320 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("user")
2025-07-29 01:32:14,333 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,336 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:32:14,341 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,345 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("usersession")
2025-07-29 01:32:14,359 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,366 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:32:14,385 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,403 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:32:14,420 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,469 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:32:14,501 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,503 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:32:14,553 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,601 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE user (
	id INTEGER NOT NULL, 
	username VARCHAR(50) NOT NULL, 
	password_hash VARCHAR(255) NOT NULL, 
	full_name VARCHAR(100) NOT NULL, 
	email VARCHAR(100), 
	role VARCHAR(20) NOT NULL, 
	is_active BOOLEAN NOT NULL, 
	is_verified BOOLEAN NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME, 
	last_login DATETIME, 
	password_changed_at DATETIME, 
	PRIMARY KEY (id)
)


2025-07-29 01:32:14,680 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.07914s] ()
2025-07-29 01:32:14,929 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE UNIQUE INDEX ix_user_username ON user (username)
2025-07-29 01:32:15,113 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.18370s] ()
2025-07-29 01:32:15,461 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE usersession (
	id INTEGER NOT NULL, 
	user_id INTEGER NOT NULL, 
	session_token VARCHAR(255) NOT NULL, 
	ip_address VARCHAR(45), 
	user_agent VARCHAR(500), 
	is_active BOOLEAN NOT NULL, 
	created_at DATETIME NOT NULL, 
	last_activity DATETIME NOT NULL, 
	expires_at DATETIME NOT NULL, 
	logged_out_at DATETIME, 
	PRIMARY KEY (id), 
	FOREIGN KEY(user_id) REFERENCES user (id)
)


2025-07-29 01:32:15,493 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.03149s] ()
2025-07-29 01:32:15,735 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE INDEX ix_usersession_user_id ON usersession (user_id)
2025-07-29 01:32:15,739 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.00463s] ()
2025-07-29 01:32:16,004 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE UNIQUE INDEX ix_usersession_session_token ON usersession (session_token)
2025-07-29 01:32:16,019 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.01526s] ()
2025-07-29 01:32:16,235 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:32:16,239 - __main__ - INFO - initialize_database:64 - Database tables created/verified
2025-07-29 01:32:16,242 - __main__ - INFO - initialize_database:75 - Skipping performance indexes for SQLite
2025-07-29 01:32:24,972 - __main__ - INFO - <module>:362 - Starting Psychiatry EMR application...
2025-07-29 01:32:24,973 - __main__ - ERROR - <module>:369 - Failed to start application: 'App' object has no attribute 'run'
2025-07-29 01:32:54,616 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 01:32:54,617 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 01:32:54,812 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:32:54,915 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:32:54,975 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:32:55,095 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:32:55,122 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:55,317 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:32:55,431 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:55,559 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:32:55,865 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:55,941 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:32:56,068 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:56,102 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:32:56,128 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:56,265 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:32:56,317 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:56,441 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:32:56,499 - __main__ - INFO - initialize_database:64 - Database tables created/verified
2025-07-29 01:32:56,564 - __main__ - INFO - initialize_database:75 - Skipping performance indexes for SQLite
2025-07-29 01:33:01,691 - __main__ - INFO - <module>:362 - Starting Psychiatry EMR application...
2025-07-29 01:33:01,692 - __main__ - ERROR - <module>:369 - Failed to start application: 'App' object has no attribute 'run'
2025-07-29 01:48:00,828 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 01:48:02,696 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:48:02,698 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 01:48:03,205 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:48:03,443 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:48:03,465 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:48:03,467 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,491 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:48:03,495 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,506 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:48:03,509 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,511 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:48:03,513 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,515 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:48:03,516 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,520 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:48:03,522 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,523 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:48:03,524 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 01:48:03,524 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 01:48:12,239 - concurrent.futures - ERROR - _invoke_callbacks:342 - exception calling callback for <Future at 0x1f5dce334d0 state=finished raised AttributeError>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\sqlalchemy\engine\base.py", line 1411, in execute
    meth = statement._execute_on_connection
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Text' object has no attribute '_execute_on_connection'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\projects\labap1py\labap1py\labap1py.py", line 220, in health_check
    conn.execute(rx.text("SELECT 1"))
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\sqlalchemy\engine\base.py", line 1413, in execute
    raise exc.ObjectNotExecutableError(statement) from err
sqlalchemy.exc.ObjectNotExecutableError: Not an executable object: {"name": "RadixThemesText", "props": ["as:\"p\""], "contents": "", "special_props": [], "children": [{"name": "", "props": [], "contents": "\"SELECT 1\"", "special_props": [], "children": [], "autofocus": false}], "autofocus": false}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Python313\Lib\concurrent\futures\_base.py", line 340, in _invoke_callbacks
    callback(self)
    ~~~~~~~~^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\app.py", line 611, in callback
    return f.result()
           ~~~~~~~~^^
  File "C:\Python313\Lib\concurrent\futures\_base.py", line 449, in result
    return self.__get_result()
           ~~~~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\concurrent\futures\_base.py", line 401, in __get_result
    raise self._exception
  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\app.py", line 1215, in _compile
    self._compile_page(route, save_page=should_compile)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\app.py", line 854, in _compile_page
    component = compiler.compile_unevaluated_page(
        route, self._unevaluated_pages[route], self.style, self.theme
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\compiler\compiler.py", line 800, in compile_unevaluated_page
    component = into_component(page.component)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\compiler\compiler.py", line 728, in into_component
    component_called = component()
  File "C:\Users\<USER>\projects\labap1py\labap1py\labap1py.py", line 230, in health_check
    return rx.json({
           ^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\__init__.py", line 371, in __getattr__
    return getattr(name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\utils\lazy_loader.py", line 88, in __getattr__
    raise AttributeError(msg)
AttributeError: No reflex attribute json
Happened while evaluating page 'health'
2025-07-29 01:49:05,603 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 01:49:05,605 - labap1py.labap1py - ERROR - initialize_application:113 - Application initialization failed: 
2025-07-29 01:51:05,528 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 01:51:05,529 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 01:51:05,631 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:51:05,899 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:51:05,907 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:51:05,909 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:51:05,911 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,916 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:51:05,917 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,918 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:51:05,920 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,923 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:51:05,924 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,926 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:51:05,928 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,932 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:51:05,937 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,939 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:51:05,940 - __main__ - INFO - initialize_database:64 - Database tables created/verified
2025-07-29 01:51:05,941 - __main__ - INFO - initialize_database:75 - Skipping performance indexes for SQLite
2025-07-29 01:51:07,755 - __main__ - INFO - <module>:362 - Starting Psychiatry EMR application...
2025-07-29 01:51:07,756 - __main__ - ERROR - <module>:369 - Failed to start application: 'App' object has no attribute 'run'
2025-07-29 01:54:53,228 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 01:54:53,230 - labap1py.labap1py - ERROR - initialize_application:113 - Application initialization failed: 
2025-07-29 01:55:44,023 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 01:55:44,199 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:55:44,200 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 01:55:44,255 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:55:44,258 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:55:44,259 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:55:44,260 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:55:44,266 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:55:44,273 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:55:44,276 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:55:44,286 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:55:44,290 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:55:44,293 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:55:44,294 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:55:44,299 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:55:44,304 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:55:44,306 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:55:44,308 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:55:44,309 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 01:55:44,310 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 01:55:44,374 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 01:55:44,374 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 01:55:44,390 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 01:55:44,390 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 01:55:44,391 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 01:55:44,392 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 01:55:44,392 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 01:55:44,393 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 01:55:44,393 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 01:55:44,394 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 01:55:44,394 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 01:55:44,395 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 01:55:44,399 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 01:55:44,403 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 01:57:38,992 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 01:57:39,111 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:57:39,114 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 01:57:39,150 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:57:39,154 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:57:39,155 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:57:39,156 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:57:39,159 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:57:39,159 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:57:39,161 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:57:39,168 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:57:39,171 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:57:39,173 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:57:39,176 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:57:39,182 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:57:39,188 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:57:39,189 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:57:39,190 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:57:39,193 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 01:57:39,194 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 01:57:39,238 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 01:57:39,240 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 01:57:39,250 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 01:57:39,254 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 01:57:39,255 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 01:57:39,255 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 01:57:39,256 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 01:57:39,257 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 01:57:39,257 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 01:57:39,258 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 01:57:39,258 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 01:57:39,260 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 01:57:39,261 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 01:57:39,274 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 01:58:22,368 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 01:58:22,478 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:58:22,478 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 01:58:22,514 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:58:22,519 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:58:22,520 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:58:22,521 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:58:22,523 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:58:22,523 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:58:22,525 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:58:22,530 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:58:22,535 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:58:22,536 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:58:22,538 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:58:22,539 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:58:22,540 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:58:22,541 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:58:22,547 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:58:22,552 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 01:58:22,552 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 01:58:22,589 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 01:58:22,590 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 01:58:22,603 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 01:58:22,605 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 01:58:22,607 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 01:58:22,608 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 01:58:22,622 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 01:58:22,623 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 01:58:22,624 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 01:58:22,625 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 01:58:22,637 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 01:58:22,641 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 01:58:22,651 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 01:58:22,653 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 02:00:02,771 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 02:00:02,973 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 02:00:02,974 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 02:00:03,030 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 02:00:03,044 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 02:00:03,045 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 02:00:03,046 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:00:03,054 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 02:00:03,060 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:00:03,066 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 02:00:03,080 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:00:03,094 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 02:00:03,095 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:00:03,096 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 02:00:03,103 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:00:03,108 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 02:00:03,110 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:00:03,113 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 02:00:03,114 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 02:00:03,117 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 02:00:03,158 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 02:00:03,159 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 02:00:03,164 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 02:00:03,168 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 02:00:03,170 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 02:00:03,173 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 02:00:03,174 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 02:00:03,176 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 02:00:03,177 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 02:00:03,177 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 02:00:03,178 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 02:00:03,178 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 02:00:03,179 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 02:00:03,179 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 02:04:15,150 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 02:04:15,474 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 02:04:15,476 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 02:04:15,567 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 02:04:15,573 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 02:04:15,580 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 02:04:15,582 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:04:15,585 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 02:04:15,590 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:04:15,592 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 02:04:15,596 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:04:15,605 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 02:04:15,607 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:04:15,611 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 02:04:15,625 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:04:15,629 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 02:04:15,630 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:04:15,633 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 02:04:15,645 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 02:04:15,646 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 02:04:15,740 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 02:04:15,742 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 02:04:15,759 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 02:04:15,762 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 02:04:15,765 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 02:04:15,766 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 02:04:15,767 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 02:04:15,768 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 02:04:15,772 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 02:04:15,775 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 02:04:15,778 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 02:04:15,782 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 02:04:15,783 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 02:04:15,785 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 02:09:09,811 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 02:09:10,401 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 02:09:10,602 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 02:09:10,761 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 02:09:10,792 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 02:09:10,901 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 02:09:10,995 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:09:11,008 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 02:09:11,115 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:09:11,135 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 02:09:11,137 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:09:11,200 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 02:09:11,204 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:09:11,216 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 02:09:11,220 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:09:11,311 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 02:09:11,344 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:09:11,349 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 02:09:11,352 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 02:09:11,352 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 02:09:11,414 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 02:09:11,415 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 02:09:11,444 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 02:09:11,446 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 02:09:11,447 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 02:09:11,450 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 02:09:11,452 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 02:09:11,458 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 02:09:11,462 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 02:09:11,464 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 02:09:11,465 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 02:09:11,467 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 02:09:11,469 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 02:09:11,483 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 02:14:25,613 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 02:14:26,201 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 02:14:26,270 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 02:14:26,483 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 02:14:26,605 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 02:14:26,724 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 02:14:26,846 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:14:27,004 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 02:14:27,110 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:14:27,249 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 02:14:27,370 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:14:27,507 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 02:14:27,569 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:14:27,663 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 02:14:27,694 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:14:27,777 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 02:14:27,824 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:14:27,928 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 02:14:27,955 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 02:14:27,960 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 02:14:28,214 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 02:14:28,229 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 02:14:28,262 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 02:14:28,275 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 02:14:28,327 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 02:14:28,340 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 02:14:28,358 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 02:14:28,374 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 02:14:28,378 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 02:14:28,402 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 02:14:28,413 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 02:14:28,427 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 02:14:28,444 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 02:14:28,461 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 11:47:25,144 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 11:47:25,764 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 11:47:25,765 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 11:47:27,113 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 11:47:27,394 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 11:47:27,411 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 11:47:27,414 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:47:27,446 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 11:47:27,449 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:47:27,454 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 11:47:27,463 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:47:27,465 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 11:47:27,466 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:47:27,473 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 11:47:27,476 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:47:27,480 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 11:47:27,481 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:47:27,483 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 11:47:27,487 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 11:47:27,487 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 11:47:27,632 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 11:47:27,633 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 11:47:27,671 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 11:47:27,675 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 11:47:27,679 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 11:47:27,693 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 11:47:27,703 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 11:47:27,706 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 11:47:27,710 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 11:47:27,712 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 11:47:27,723 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 11:47:27,724 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 11:47:27,757 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 11:47:27,766 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 11:52:23,277 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 11:52:23,401 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 11:52:23,402 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 11:52:23,833 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 11:52:23,838 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 11:52:23,840 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 11:52:23,844 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:52:23,847 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 11:52:23,848 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:52:23,849 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 11:52:23,851 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:52:23,857 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 11:52:23,861 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:52:23,863 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 11:52:23,865 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:52:23,866 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 11:52:23,867 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:52:23,872 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 11:52:23,877 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 11:52:23,878 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 11:52:23,917 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 11:52:23,918 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 11:52:23,932 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 11:52:23,933 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 11:52:23,934 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 11:52:23,934 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 11:52:23,935 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 11:52:23,942 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 11:52:23,944 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 11:52:23,945 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 11:52:23,946 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 11:52:23,947 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 11:52:23,948 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 11:52:23,949 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 11:53:46,655 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 11:53:46,843 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 11:53:46,844 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 11:53:46,903 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 11:53:46,911 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 11:53:46,912 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 11:53:46,913 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:53:46,919 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 11:53:46,926 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:53:46,927 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 11:53:46,929 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:53:46,931 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 11:53:46,931 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:53:46,941 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 11:53:46,942 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:53:46,945 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 11:53:46,946 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:53:46,947 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 11:53:46,948 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 11:53:46,955 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 11:53:46,994 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 11:53:46,995 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 11:53:47,005 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 11:53:47,008 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 11:53:47,009 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 11:53:47,009 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 11:53:47,010 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 11:53:47,012 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 11:53:47,013 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 11:53:47,014 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 11:53:47,015 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 11:53:47,020 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 11:53:47,023 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 11:53:47,024 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 11:56:13,027 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 11:56:13,379 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 11:56:13,432 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 11:56:13,649 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 11:56:13,765 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 11:56:13,892 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 11:56:14,038 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:56:14,114 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 11:56:14,165 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:56:14,212 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 11:56:14,231 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:56:14,283 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 11:56:14,345 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:56:14,364 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 11:56:14,411 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:56:14,464 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 11:56:14,576 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:56:14,627 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 11:56:14,670 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 11:56:14,729 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 11:56:14,990 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 11:56:15,132 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 11:56:15,312 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 11:56:15,329 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 11:56:15,349 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 11:56:15,392 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 11:56:15,397 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 11:56:15,399 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 11:56:15,408 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 11:56:15,414 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 11:56:15,427 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 11:56:15,439 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 11:56:15,464 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 11:56:15,480 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 11:58:43,094 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 11:58:43,265 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 11:58:43,266 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 11:58:43,314 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 11:58:43,316 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 11:58:43,320 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 11:58:43,326 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:58:43,328 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 11:58:43,329 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:58:43,331 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 11:58:43,332 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:58:43,333 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 11:58:43,340 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:58:43,344 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 11:58:43,346 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:58:43,348 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 11:58:43,349 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:58:43,357 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 11:58:43,362 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 11:58:43,362 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 11:58:43,481 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 11:58:43,482 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 11:58:43,491 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 11:58:43,495 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 11:58:43,497 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 11:58:43,499 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 11:58:43,503 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 11:58:43,504 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 11:58:43,505 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 11:58:43,506 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 11:58:43,507 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 11:58:43,512 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 11:58:43,520 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 11:58:43,523 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:07:41,941 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:07:42,470 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:07:42,569 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:07:43,171 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:07:43,314 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:07:43,343 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:07:43,354 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:07:43,390 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:07:43,392 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:07:43,397 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:07:43,409 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:07:43,413 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:07:43,421 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:07:43,424 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:07:43,426 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:07:43,442 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:07:43,455 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:07:43,464 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:07:43,476 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:07:43,477 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:07:43,646 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:07:43,654 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:07:43,686 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:07:43,691 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:07:43,692 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:07:43,693 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:07:43,693 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:07:43,699 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:07:43,704 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:07:43,707 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:07:43,708 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:07:43,826 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:07:43,993 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:07:44,160 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:12:13,594 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:12:13,730 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:12:13,731 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:12:13,771 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:12:13,773 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:12:13,785 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:12:13,786 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:12:13,789 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:12:13,791 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:12:13,800 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:12:13,803 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:12:13,806 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:12:13,821 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:12:13,824 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:12:13,830 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:12:13,837 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:12:13,838 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:12:13,840 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:12:13,845 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:12:13,848 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:12:13,888 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:12:13,889 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:12:13,904 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:12:13,905 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:12:13,906 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:12:13,906 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:12:13,907 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:12:13,911 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:12:13,913 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:12:13,917 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:12:13,920 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:12:13,920 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:12:13,921 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:12:13,921 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:14:45,251 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:14:45,432 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:14:45,433 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:14:45,603 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:14:45,622 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:14:45,636 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:14:45,639 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:14:45,664 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:14:45,754 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:14:45,836 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:14:45,936 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:14:45,946 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:14:45,978 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:14:45,996 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:14:46,005 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:14:46,015 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:14:46,069 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:14:46,089 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:14:46,120 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:14:46,133 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:14:46,249 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:14:46,250 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:14:46,256 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:14:46,262 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:14:46,266 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:14:46,267 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:14:46,268 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:14:46,269 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:14:46,270 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:14:46,271 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:14:46,272 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:14:46,284 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:14:46,285 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:14:46,288 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:17:22,150 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:17:22,329 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:17:22,337 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:17:22,434 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:17:22,449 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:17:22,465 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:17:22,468 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:17:22,473 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:17:22,489 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:17:22,548 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:17:22,558 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:17:22,573 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:17:22,593 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:17:22,666 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:17:22,676 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:17:22,691 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:17:22,706 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:17:22,725 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:17:22,743 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:17:22,747 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:17:22,842 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:17:22,847 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:17:22,858 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:17:22,860 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:17:22,864 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:17:22,865 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:17:22,870 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:17:22,873 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:17:22,874 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:17:22,875 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:17:22,876 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:17:22,877 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:17:22,881 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:17:22,882 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:20:04,325 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:20:04,460 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:20:04,460 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:20:04,506 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:20:04,512 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:20:04,513 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:20:04,514 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:20:04,520 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:20:04,524 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:20:04,525 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:20:04,527 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:20:04,528 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:20:04,530 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:20:04,531 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:20:04,539 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:20:04,542 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:20:04,553 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:20:04,562 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:20:04,576 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:20:04,578 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:20:04,636 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:20:04,655 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:20:04,665 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:20:04,679 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:20:04,685 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:20:04,714 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:20:04,760 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:20:04,778 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:20:04,782 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:20:04,792 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:20:04,794 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:20:04,831 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:20:04,846 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:20:04,876 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:25:44,910 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:25:45,056 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:25:45,060 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:25:45,110 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:25:45,117 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:25:45,128 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:25:45,132 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:25:45,135 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:25:45,149 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:25:45,152 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:25:45,154 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:25:45,157 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:25:45,159 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:25:45,168 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:25:45,170 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:25:45,174 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:25:45,177 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:25:45,184 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:25:45,185 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:25:45,186 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:25:45,244 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:25:45,246 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:25:45,262 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:25:45,267 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:25:45,269 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:25:45,270 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:25:45,274 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:25:45,275 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:25:45,277 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:25:45,283 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:25:45,284 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:25:45,285 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:25:45,286 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:25:45,288 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:29:02,174 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:29:02,272 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:29:02,272 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:29:02,297 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:29:02,303 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:29:02,304 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:29:02,311 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:02,318 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:29:02,332 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:02,336 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:29:02,390 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:02,392 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:29:02,396 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:02,399 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:29:02,400 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:02,413 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:29:02,416 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:02,420 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:29:02,426 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:29:02,429 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:29:02,456 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:29:02,457 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:29:02,468 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:29:02,473 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:29:02,475 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:29:02,476 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:29:02,480 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:29:02,481 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:29:02,484 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:29:02,487 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:29:02,489 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:29:02,492 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:29:02,533 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:29:02,575 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:29:59,511 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:29:59,617 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:29:59,618 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:29:59,641 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:29:59,644 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:29:59,648 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:29:59,652 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:59,665 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:29:59,667 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:59,671 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:29:59,682 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:59,683 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:29:59,684 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:59,689 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:29:59,690 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:59,694 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:29:59,698 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:59,699 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:29:59,700 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:29:59,700 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:29:59,733 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:29:59,734 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:29:59,746 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:29:59,747 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:29:59,748 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:29:59,749 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:29:59,749 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:29:59,750 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:29:59,750 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:29:59,751 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:29:59,754 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:29:59,755 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:29:59,756 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:29:59,759 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:32:06,376 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:32:06,599 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:32:06,607 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:32:06,684 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:32:06,687 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:32:06,689 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:32:06,692 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:32:06,698 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:32:06,718 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:32:06,728 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:32:06,744 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:32:06,760 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:32:06,763 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:32:06,766 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:32:06,776 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:32:06,778 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:32:06,787 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:32:06,792 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:32:06,794 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:32:06,799 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:32:06,860 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:32:06,861 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:32:06,887 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:32:06,890 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:32:06,891 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:32:06,892 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:32:06,893 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:32:06,894 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:32:06,894 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:32:06,903 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:32:06,905 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:32:06,907 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:32:06,908 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:32:06,909 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:35:58,654 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:35:58,855 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:35:58,855 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:35:58,938 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:35:58,945 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:35:58,948 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:35:58,951 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:35:58,955 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:35:58,956 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:35:58,958 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:35:58,963 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:35:58,969 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:35:58,973 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:35:58,977 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:35:58,985 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:35:58,988 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:35:58,989 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:35:58,997 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:35:59,000 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:35:59,001 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:35:59,037 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:35:59,038 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:35:59,049 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:35:59,049 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:35:59,050 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:35:59,051 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:35:59,052 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:35:59,053 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:35:59,054 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:35:59,054 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:35:59,055 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:35:59,056 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:35:59,057 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:35:59,058 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
