"""
Psychiatry EMR - Patient Management UI Components
Patient data entry, search interface, and duplicate detection UI.
"""

import reflex as rx
from typing import List, Dict, Any
from datetime import date

from states.patient_state import PatientState

def patient_search_component() -> rx.Component:
    """Patient search interface with real-time results"""
    return rx.vstack(
        # Search header
        rx.heading("Patient Search", size="lg", mb=4),
        
        # Search input
        rx.hstack(
            rx.input(
                placeholder="Search by name, DOB, phone, or email...",
                value=PatientState.search_term,
                on_change=PatientState.set_search_term,
                width="100%",
                size="lg"
            ),
            rx.button(
                "Search",
                on_click=PatientState.search_patients,
                loading=PatientState.is_loading,
                size="lg",
                color_scheme="blue"
            ),
            width="100%",
            spacing=2
        ),
        
        # Results section
        rx.cond(
            PatientState.search_results,
            rx.vstack(
                # Results header
                rx.hstack(
                    rx.text(f"Found {PatientState.total_results} patients"),
                    rx.spacer(),
                    # Pagination controls
                    rx.hstack(
                        rx.button(
                            "Previous",
                            on_click=PatientState.prev_page,
                            disabled=PatientState.current_page == 1,
                            size="sm"
                        ),
                        rx.text(f"Page {PatientState.current_page} of {PatientState.total_pages}"),
                        rx.button(
                            "Next", 
                            on_click=PatientState.next_page,
                            disabled=PatientState.current_page == PatientState.total_pages,
                            size="sm"
                        ),
                        spacing=2
                    ),
                    width="100%"
                ),
                
                # Results table
                rx.table.root(
                    rx.table.header(
                        rx.table.row(
                            rx.table.column_header_cell("Name"),
                            rx.table.column_header_cell("Date of Birth"),
                            rx.table.column_header_cell("Phone"),
                            rx.table.column_header_cell("Email"),
                            rx.table.column_header_cell("Actions")
                        )
                    ),
                    rx.table.body(
                        rx.foreach(
                            PatientState.search_results,
                            lambda patient: rx.table.row(
                                rx.table.cell(f"{patient.first_name} {patient.last_name}"),
                                rx.table.cell(patient.date_of_birth.strftime("%Y-%m-%d") if patient.date_of_birth else ""),
                                rx.table.cell(patient.phone_number or ""),
                                rx.table.cell(patient.email or ""),
                                rx.table.cell(
                                    rx.hstack(
                                        rx.button(
                                            "View",
                                            on_click=lambda: PatientState.load_patient(patient.id),
                                            size="sm",
                                            color_scheme="blue"
                                        ),
                                        rx.button(
                                            "Edit",
                                            size="sm",
                                            color_scheme="gray"
                                        ),
                                        spacing=1
                                    )
                                )
                            )
                        )
                    ),
                    width="100%"
                ),
                width="100%",
                spacing=3
            )
        ),
        
        # No results message
        rx.cond(
            PatientState.search_term & (PatientState.total_results == 0) & ~PatientState.is_loading,
            rx.text("No patients found matching your search.", color="gray.500")
        ),
        
        width="100%",
        spacing=4
    )

def patient_form_component() -> rx.Component:
    """Patient data entry form with validation"""
    return rx.vstack(
        # Form header
        rx.heading("Patient Information", size="lg", mb=4),
        
        # Personal information section
        rx.vstack(
            rx.heading("Personal Information", size="md", mb=2),
            
            # Name fields
            rx.hstack(
                rx.vstack(
                    rx.text("First Name *", font_weight="bold"),
                    rx.input(
                        placeholder="First name",
                        required=True,
                        width="100%"
                    ),
                    width="50%"
                ),
                rx.vstack(
                    rx.text("Last Name *", font_weight="bold"),
                    rx.input(
                        placeholder="Last name",
                        required=True,
                        width="100%"
                    ),
                    width="50%"
                ),
                width="100%",
                spacing=4
            ),
            
            # Date of birth and gender
            rx.hstack(
                rx.vstack(
                    rx.text("Date of Birth *", font_weight="bold"),
                    rx.input(
                        type="date",
                        required=True,
                        width="100%"
                    ),
                    width="50%"
                ),
                rx.vstack(
                    rx.text("Gender", font_weight="bold"),
                    rx.select(
                        ["Male", "Female", "Non-binary", "Other", "Prefer not to say"],
                        placeholder="Select gender",
                        width="100%"
                    ),
                    width="50%"
                ),
                width="100%",
                spacing=4
            ),
            
            width="100%",
            spacing=3,
            p=4,
            border="1px solid",
            border_color="gray.200",
            border_radius="md"
        ),
        
        # Contact information section
        rx.vstack(
            rx.heading("Contact Information", size="md", mb=2),
            
            # Phone and email
            rx.hstack(
                rx.vstack(
                    rx.text("Phone Number", font_weight="bold"),
                    rx.input(
                        placeholder="(*************",
                        type="tel",
                        width="100%"
                    ),
                    width="50%"
                ),
                rx.vstack(
                    rx.text("Email", font_weight="bold"),
                    rx.input(
                        placeholder="<EMAIL>",
                        type="email",
                        width="100%"
                    ),
                    width="50%"
                ),
                width="100%",
                spacing=4
            ),
            
            # Address
            rx.vstack(
                rx.text("Address", font_weight="bold"),
                rx.input(
                    placeholder="Street address",
                    width="100%"
                ),
                rx.hstack(
                    rx.input(
                        placeholder="City",
                        width="40%"
                    ),
                    rx.input(
                        placeholder="State",
                        width="20%"
                    ),
                    rx.input(
                        placeholder="ZIP Code",
                        width="40%"
                    ),
                    width="100%",
                    spacing=2
                ),
                width="100%",
                spacing=2
            ),
            
            width="100%",
            spacing=3,
            p=4,
            border="1px solid",
            border_color="gray.200",
            border_radius="md"
        ),
        
        # Emergency contact section
        rx.vstack(
            rx.heading("Emergency Contact", size="md", mb=2),
            
            rx.hstack(
                rx.vstack(
                    rx.text("Contact Name", font_weight="bold"),
                    rx.input(
                        placeholder="Emergency contact name",
                        width="100%"
                    ),
                    width="50%"
                ),
                rx.vstack(
                    rx.text("Relationship", font_weight="bold"),
                    rx.input(
                        placeholder="Relationship to patient",
                        width="100%"
                    ),
                    width="50%"
                ),
                width="100%",
                spacing=4
            ),
            
            rx.hstack(
                rx.vstack(
                    rx.text("Contact Phone", font_weight="bold"),
                    rx.input(
                        placeholder="(*************",
                        type="tel",
                        width="100%"
                    ),
                    width="50%"
                ),
                rx.vstack(
                    rx.text("Contact Email", font_weight="bold"),
                    rx.input(
                        placeholder="<EMAIL>",
                        type="email",
                        width="100%"
                    ),
                    width="50%"
                ),
                width="100%",
                spacing=4
            ),
            
            width="100%",
            spacing=3,
            p=4,
            border="1px solid",
            border_color="gray.200",
            border_radius="md"
        ),
        
        # Form actions
        rx.hstack(
            rx.button(
                "Check for Duplicates",
                on_click=PatientState.check_duplicates,
                color_scheme="yellow",
                size="lg"
            ),
            rx.spacer(),
            rx.button(
                "Cancel",
                color_scheme="gray",
                size="lg"
            ),
            rx.button(
                "Save Patient",
                on_click=PatientState.create_patient,
                loading=PatientState.is_loading,
                color_scheme="green",
                size="lg"
            ),
            width="100%"
        ),
        
        width="100%",
        spacing=6,
        max_width="800px"
    )

def duplicate_detection_modal() -> rx.Component:
    """Modal for displaying potential duplicate patients"""
    return rx.modal.root(
        rx.modal.trigger(
            rx.button("Check Duplicates", display="none")  # Hidden trigger
        ),
        rx.modal.content(
            rx.modal.header(
                rx.modal.title("Potential Duplicate Patients Found"),
                rx.modal.description(
                    "The following patients may be duplicates. Please review before creating a new patient record."
                )
            ),
            rx.modal.body(
                rx.cond(
                    PatientState.potential_duplicates,
                    rx.vstack(
                        rx.foreach(
                            PatientState.potential_duplicates,
                            lambda dup: rx.card(
                                rx.vstack(
                                    rx.hstack(
                                        rx.heading(dup["name"], size="sm"),
                                        rx.spacer(),
                                        rx.badge(
                                            f"{dup['similarity_score']*100:.0f}% match",
                                            color_scheme="orange"
                                        )
                                    ),
                                    rx.text(f"DOB: {dup['date_of_birth']}", color="gray.600"),
                                    rx.text(f"Phone: {dup['phone']}", color="gray.600"),
                                    rx.hstack(
                                        rx.button(
                                            "View Details",
                                            size="sm",
                                            color_scheme="blue"
                                        ),
                                        rx.button(
                                            "Merge with This Patient",
                                            size="sm",
                                            color_scheme="green"
                                        ),
                                        spacing=2
                                    ),
                                    spacing=2,
                                    align="start"
                                ),
                                width="100%",
                                p=3
                            )
                        ),
                        spacing=3,
                        width="100%"
                    ),
                    rx.text("No potential duplicates found.", color="green.600")
                )
            ),
            rx.modal.footer(
                rx.hstack(
                    rx.button(
                        "Create New Patient Anyway",
                        on_click=PatientState.create_patient,
                        color_scheme="red"
                    ),
                    rx.button(
                        "Cancel",
                        color_scheme="gray"
                    ),
                    spacing=2
                )
            ),
            max_width="600px"
        ),
        open=PatientState.potential_duplicates.length() > 0
    )

def patient_details_card(patient) -> rx.Component:
    """Patient details display card"""
    return rx.card(
        rx.vstack(
            # Header with patient name and ID
            rx.hstack(
                rx.heading(f"{patient.first_name} {patient.last_name}", size="lg"),
                rx.spacer(),
                rx.badge(f"ID: {patient.id}", color_scheme="blue"),
                width="100%"
            ),

            # Patient information grid
            rx.grid(
                # Personal info
                rx.vstack(
                    rx.text("Personal Information", font_weight="bold", color="gray.700"),
                    rx.text(f"Date of Birth: {patient.date_of_birth.strftime('%Y-%m-%d') if patient.date_of_birth else 'Not provided'}"),
                    rx.text(f"Gender: {patient.gender or 'Not specified'}"),
                    rx.text(f"Age: {patient.age if hasattr(patient, 'age') else 'Unknown'}"),
                    align="start",
                    spacing=1
                ),

                # Contact info
                rx.vstack(
                    rx.text("Contact Information", font_weight="bold", color="gray.700"),
                    rx.text(f"Phone: {patient.phone_number or 'Not provided'}"),
                    rx.text(f"Email: {patient.email or 'Not provided'}"),
                    rx.text(f"Address: {patient.address or 'Not provided'}"),
                    align="start",
                    spacing=1
                ),

                # Emergency contact
                rx.vstack(
                    rx.text("Emergency Contact", font_weight="bold", color="gray.700"),
                    rx.text(f"Name: {patient.emergency_contact_name or 'Not provided'}"),
                    rx.text(f"Phone: {patient.emergency_contact_phone or 'Not provided'}"),
                    rx.text(f"Relationship: {patient.emergency_contact_relationship or 'Not provided'}"),
                    align="start",
                    spacing=1
                ),

                # Status info
                rx.vstack(
                    rx.text("Status", font_weight="bold", color="gray.700"),
                    rx.text(f"Created: {patient.created_at.strftime('%Y-%m-%d') if patient.created_at else 'Unknown'}"),
                    rx.text(f"Last Updated: {patient.updated_at.strftime('%Y-%m-%d') if patient.updated_at else 'Never'}"),
                    rx.badge(
                        "Active" if patient.is_active else "Inactive",
                        color_scheme="green" if patient.is_active else "red"
                    ),
                    align="start",
                    spacing=1
                ),

                columns="2",
                spacing="4",
                width="100%"
            ),

            # Action buttons
            rx.hstack(
                rx.button(
                    "Edit Patient",
                    color_scheme="blue",
                    size="sm"
                ),
                rx.button(
                    "View Assessments",
                    color_scheme="green",
                    size="sm"
                ),
                rx.button(
                    "New Assessment",
                    color_scheme="purple",
                    size="sm"
                ),
                rx.spacer(),
                rx.button(
                    "Print Summary",
                    color_scheme="gray",
                    size="sm"
                ),
                width="100%"
            ),

            spacing=4,
            align="start"
        ),
        width="100%",
        p=6
    )
