['@radix-ui/themes@3.2.1', 'lucide-react@0.525.0', 'react-debounce-input@3.3.0', 'react-error-boundary@6.0.0', 'sonner@2.0.6'],{"app_name": "labap1py", "app_module_import": null, "loglevel": "default", "frontend_port": 3000, "frontend_path": "", "backend_port": 8000, "api_url": "http://localhost:8000", "deploy_url": "http://localhost:3000", "backend_host": "0.0.0.0", "db_url": "sqlite:///reflex.db", "async_db_url": null, "redis_url": null, "telemetry_enabled": true, "bun_path": "C:/Users/<USER>/AppData/Local/reflex/bun/bin/bun.exe", "static_page_generation_timeout": 60, "cors_allowed_origins": ["*"], "react_strict_mode": true, "frontend_packages": [], "state_manager_mode": "disk", "redis_lock_expiration": 10000, "redis_lock_warning_threshold": 1000, "redis_token_expiration": 3600, "_non_default_attributes": ["backend_port", "app_name", "frontend_port"], "env_file": null, "state_auto_setters": true, "show_built_with_reflex": true, "is_reflex_cloud": false, "extra_overlay_function": null, "plugins": [null], "disable_plugins": []}