"""
Psychiatry EMR - DSM-5-TR Diagnostic Forms
Dynamic criteria forms, progress tracking, and evaluation display.
"""

import reflex as rx
from typing import Dict, Any

from states.clinical_state import ClinicalState

def disorder_selection_component() -> rx.Component:
    """Disorder selection dropdown with search"""
    return rx.vstack(
        rx.heading("DSM-5-TR Diagnostic Criteria", size="lg", mb=4),
        
        # Disorder selection
        rx.vstack(
            rx.text("Select Disorder", font_weight="bold", mb=2),
            rx.select(
                rx.foreach(
                    ClinicalState.available_disorders,
                    lambda disorder: rx.option(
                        disorder["name"],
                        value=disorder["code"]
                    )
                ),
                placeholder="Choose a disorder to evaluate...",
                value=ClinicalState.selected_disorder,
                on_change=ClinicalState.select_disorder,
                width="100%",
                size="lg"
            ),
            width="100%",
            spacing=2
        ),
        
        # Load disorders button if not loaded
        rx.cond(
            ClinicalState.available_disorders.length() == 0,
            rx.button(
                "Load Available Disorders",
                on_click=ClinicalState.load_disorders,
                color_scheme="blue",
                size="lg"
            )
        ),
        
        width="100%",
        spacing=4
    )

def criteria_evaluation_form() -> rx.Component:
    """Dynamic criteria evaluation form"""
    return rx.cond(
        ClinicalState.disorder_criteria,
        rx.vstack(
            # Disorder header
            rx.card(
                rx.vstack(
                    rx.heading(ClinicalState.disorder_criteria.name, size="md"),
                    rx.text(f"Code: {ClinicalState.disorder_criteria.disorder_code}", color="gray.600"),
                    rx.text(
                        f"Minimum criteria required: {ClinicalState.disorder_criteria.minimum_criteria}",
                        font_weight="bold"
                    ),
                    rx.cond(
                        ClinicalState.disorder_criteria.duration_required,
                        rx.text("Duration requirements apply", color="orange.600", font_style="italic")
                    ),
                    spacing=2,
                    align="start"
                ),
                width="100%",
                p=4,
                bg="blue.50"
            ),
            
            # Progress indicator
            diagnostic_progress_component(),
            
            # Criteria checklist
            rx.vstack(
                rx.heading("Diagnostic Criteria", size="md", mb=3),
                
                rx.foreach(
                    ClinicalState.disorder_criteria.criteria,
                    lambda criterion: criterion_checkbox_component(criterion)
                ),
                
                width="100%",
                spacing=3,
                p=4,
                border="1px solid",
                border_color="gray.200",
                border_radius="md"
            ),
            
            # Evaluation results
            rx.cond(
                ClinicalState.dsm5_evaluation,
                evaluation_results_component()
            ),
            
            width="100%",
            spacing=6
        )
    )

def criterion_checkbox_component(criterion) -> rx.Component:
    """Individual criterion checkbox with description"""
    return rx.card(
        rx.hstack(
            # Checkbox
            rx.checkbox(
                checked=ClinicalState.criterion_responses.get(criterion.id, False),
                on_change=lambda value: ClinicalState.update_criterion_response(criterion.id, value),
                size="lg"
            ),
            
            # Criterion content
            rx.vstack(
                rx.hstack(
                    rx.text(criterion.description, font_weight="medium"),
                    rx.cond(
                        criterion.required,
                        rx.badge("Required", color_scheme="red", size="sm")
                    ),
                    spacing=2,
                    align="center"
                ),
                rx.text(f"Criterion ID: {criterion.id}", color="gray.500", font_size="sm"),
                spacing=1,
                align="start",
                flex="1"
            ),
            
            # Status indicator
            rx.cond(
                ClinicalState.criterion_responses.get(criterion.id, False),
                rx.icon("check-circle", color="green.500", size=20),
                rx.icon("circle", color="gray.300", size=20)
            ),
            
            width="100%",
            align="center",
            spacing=3
        ),
        width="100%",
        p=3,
        _hover={"bg": "gray.50"}
    )

def diagnostic_progress_component() -> rx.Component:
    """Progress tracking for diagnostic criteria"""
    return rx.card(
        rx.vstack(
            rx.hstack(
                rx.text("Diagnostic Progress", font_weight="bold"),
                rx.spacer(),
                rx.text(ClinicalState.required_criteria_status, color="blue.600"),
                width="100%"
            ),
            
            # Progress bar
            rx.progress(
                value=ClinicalState.criteria_met_percentage,
                width="100%",
                color_scheme="blue",
                size="lg"
            ),
            
            # Confidence level
            rx.hstack(
                rx.text("Confidence Level:", font_weight="medium"),
                rx.badge(
                    ClinicalState.diagnosis_confidence,
                    color_scheme=rx.cond(
                        ClinicalState.diagnosis_confidence == "High confidence",
                        "green",
                        rx.cond(
                            ClinicalState.diagnosis_confidence == "Moderate confidence",
                            "yellow",
                            "red"
                        )
                    )
                ),
                spacing=2
            ),
            
            spacing=3,
            width="100%"
        ),
        width="100%",
        p=4,
        bg="gray.50"
    )

def evaluation_results_component() -> rx.Component:
    """Display evaluation results and recommendations"""
    return rx.card(
        rx.vstack(
            rx.heading("Evaluation Results", size="md", mb=3),
            
            # Main result
            rx.hstack(
                rx.text("Diagnostic Criteria:", font_weight="bold"),
                rx.badge(
                    rx.cond(
                        ClinicalState.dsm5_evaluation["criteria_met"],
                        "CRITERIA MET",
                        "CRITERIA NOT MET"
                    ),
                    color_scheme=rx.cond(
                        ClinicalState.dsm5_evaluation["criteria_met"],
                        "green",
                        "red"
                    ),
                    size="lg"
                ),
                spacing=3
            ),
            
            # Detailed breakdown
            rx.grid(
                rx.vstack(
                    rx.text("Criteria Summary", font_weight="bold", color="gray.700"),
                    rx.text(f"Met: {ClinicalState.dsm5_evaluation['met_criteria_count']}"),
                    rx.text(f"Required: {ClinicalState.dsm5_evaluation['required_criteria']}"),
                    rx.text(f"Total: {len(ClinicalState.criterion_responses)}"),
                    align="start",
                    spacing=1
                ),
                
                rx.vstack(
                    rx.text("Confidence Metrics", font_weight="bold", color="gray.700"),
                    rx.text(f"Confidence: {ClinicalState.dsm5_evaluation.get('confidence_level', 0):.1%}"),
                    rx.text(f"Duration Required: {'Yes' if ClinicalState.dsm5_evaluation.get('duration_required') else 'No'}"),
                    align="start",
                    spacing=1
                ),
                
                columns="2",
                spacing="4",
                width="100%"
            ),
            
            # Met criteria list
            rx.cond(
                ClinicalState.dsm5_evaluation["met_criteria_ids"],
                rx.vstack(
                    rx.text("Met Criteria:", font_weight="bold", color="green.700"),
                    rx.foreach(
                        ClinicalState.dsm5_evaluation["met_criteria_ids"],
                        lambda criterion_id: rx.text(f"• {criterion_id}", color="green.600")
                    ),
                    spacing=1,
                    align="start"
                )
            ),
            
            # Clinical recommendations
            rx.vstack(
                rx.text("Clinical Recommendations", font_weight="bold", color="blue.700"),
                rx.cond(
                    ClinicalState.dsm5_evaluation["criteria_met"],
                    rx.vstack(
                        rx.text("✓ Diagnostic criteria are met for this disorder", color="green.600"),
                        rx.text("• Consider differential diagnoses", color="blue.600"),
                        rx.text("• Evaluate for comorbid conditions", color="blue.600"),
                        rx.text("• Assess functional impairment", color="blue.600"),
                        spacing=1,
                        align="start"
                    ),
                    rx.vstack(
                        rx.text("✗ Diagnostic criteria are not fully met", color="red.600"),
                        rx.text("• Consider other disorders in differential", color="blue.600"),
                        rx.text("• Re-evaluate symptoms over time", color="blue.600"),
                        rx.text("• Consider subsyndromal presentations", color="blue.600"),
                        spacing=1,
                        align="start"
                    )
                ),
                spacing=2,
                align="start"
            ),
            
            spacing=4,
            align="start"
        ),
        width="100%",
        p=4,
        border="2px solid",
        border_color=rx.cond(
            ClinicalState.dsm5_evaluation["criteria_met"],
            "green.200",
            "red.200"
        ),
        bg=rx.cond(
            ClinicalState.dsm5_evaluation["criteria_met"],
            "green.50",
            "red.50"
        )
    )

def dsm5_action_buttons() -> rx.Component:
    """Action buttons for DSM-5 evaluation"""
    return rx.hstack(
        rx.button(
            "Reset Evaluation",
            on_click=ClinicalState.reset_assessment_form,
            color_scheme="gray",
            size="lg"
        ),
        rx.spacer(),
        rx.button(
            "Save to Assessment",
            on_click=ClinicalState.create_assessment,
            loading=ClinicalState.is_loading,
            color_scheme="green",
            size="lg",
            disabled=~ClinicalState.selected_disorder
        ),
        width="100%"
    )
