"""
Psychiatry EMR - Authentication State
User authentication state management with session handling.
"""

import reflex as rx
from typing import Optional
import logging

from models.user import UserData, LoginRequest, LoginResponse
from auth.auth_service import AuthService
from services.database import get_db_session

logger = logging.getLogger(__name__)

class AuthState(rx.State):
    """Authentication state management"""
    
    # User state
    current_user: Optional[UserData] = None
    is_authenticated: bool = False
    session_token: Optional[str] = None
    
    # Login form state
    login_username: str = ""
    login_password: str = ""
    
    # UI state
    is_loading: bool = False
    error_message: str = ""
    success_message: str = ""
    
    @rx.var
    def is_admin(self) -> bool:
        """Check if current user is admin"""
        return self.current_user and self.current_user.role == "admin"
    
    @rx.var
    def is_supervisor(self) -> bool:
        """Check if current user is supervisor or admin"""
        return self.current_user and self.current_user.role in ["supervisor", "admin"]
    
    @rx.var
    def current_user_id(self) -> int:
        """Get current user ID"""
        return self.current_user.id if self.current_user else 0
    
    def login(self):
        """Authenticate user"""
        if not self.login_username.strip() or not self.login_password:
            self.show_error("Username and password are required")
            return
        
        self.is_loading = True
        self.clear_messages()
        
        try:
            with get_db_session() as db:
                auth_service = AuthService(db)
                
                # Get client info (would be enhanced with real client data)
                ip_address = "127.0.0.1"  # Placeholder
                user_agent = "Reflex Client"  # Placeholder
                
                auth_result = auth_service.authenticate_user(
                    self.login_username,
                    self.login_password,
                    ip_address,
                    user_agent
                )
                
                if auth_result:
                    # Set user state
                    self.current_user = UserData(
                        id=auth_result["user_id"],
                        username=auth_result["username"],
                        full_name=auth_result["full_name"],
                        role=auth_result["role"],
                        is_active=True,
                        created_at=auth_result.get("created_at"),
                        last_login=auth_result.get("last_login")
                    )
                    
                    self.is_authenticated = True
                    self.session_token = auth_result["session_token"]
                    
                    # Clear login form
                    self.login_username = ""
                    self.login_password = ""
                    
                    self.show_success(f"Welcome, {auth_result['full_name']}!")
                    logger.info(f"User logged in: {auth_result['username']}")
                    
                    # Redirect to dashboard (would be implemented with Reflex routing)
                    return rx.redirect("/dashboard")
                else:
                    self.show_error("Invalid username or password")
                    logger.warning(f"Failed login attempt: {self.login_username}")
                    
        except Exception as e:
            logger.error(f"Login error: {e}")
            self.show_error("Login failed. Please try again.")
        finally:
            self.is_loading = False
    
    def logout(self):
        """Logout current user"""
        if not self.session_token:
            return
        
        try:
            with get_db_session() as db:
                auth_service = AuthService(db)
                auth_service.logout_user(self.session_token)
            
            # Clear user state
            self.current_user = None
            self.is_authenticated = False
            self.session_token = None
            
            self.show_success("Logged out successfully")
            logger.info("User logged out")
            
            # Redirect to login page
            return rx.redirect("/login")
            
        except Exception as e:
            logger.error(f"Logout error: {e}")
            self.show_error("Logout failed")
    
    def validate_session(self):
        """Validate current session"""
        if not self.session_token:
            return False
        
        try:
            with get_db_session() as db:
                auth_service = AuthService(db)
                user_info = auth_service.validate_session(self.session_token)
                
                if user_info:
                    # Update user state if needed
                    if not self.current_user or self.current_user.id != user_info["user_id"]:
                        self.current_user = UserData(
                            id=user_info["user_id"],
                            username=user_info["username"],
                            full_name=user_info["full_name"],
                            role=user_info["role"],
                            is_active=True,
                            created_at=None,  # Would be loaded if needed
                            last_login=None
                        )
                    
                    self.is_authenticated = True
                    return True
                else:
                    # Session invalid, clear state
                    self.current_user = None
                    self.is_authenticated = False
                    self.session_token = None
                    return False
                    
        except Exception as e:
            logger.error(f"Session validation error: {e}")
            return False
    
    def require_auth(self):
        """Require authentication - redirect to login if not authenticated"""
        if not self.is_authenticated:
            return rx.redirect("/login")
        return None
    
    def require_admin(self):
        """Require admin role - redirect if not admin"""
        if not self.is_authenticated:
            return rx.redirect("/login")
        if not self.is_admin:
            self.show_error("Admin access required")
            return rx.redirect("/dashboard")
        return None
    
    def update_login_form(self, field: str, value: str):
        """Update login form fields"""
        if field == "username":
            self.login_username = value
        elif field == "password":
            self.login_password = value
    
    def show_success(self, message: str):
        """Show success notification"""
        self.success_message = message
        self.error_message = ""
    
    def show_error(self, message: str):
        """Show error notification"""
        self.error_message = message
        self.success_message = ""
    
    def clear_messages(self):
        """Clear all notification messages"""
        self.error_message = ""
        self.success_message = ""
    
    def on_load(self):
        """Called when page loads - validate session"""
        if self.session_token:
            self.validate_session()
