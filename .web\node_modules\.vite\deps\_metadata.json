{"hash": "9bc3fabc", "configHash": "e67215ea", "lockfileHash": "54ca939a", "browserHash": "d231b8bd", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "f7e36428", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "3328350c", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "83f36a99", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "c3f88215", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "7447d376", "needsInterop": true}, "react-router": {"src": "../../react-router/dist/development/index.mjs", "file": "react-router.js", "fileHash": "2bddb87c", "needsInterop": false}, "react-router/dom": {"src": "../../react-router/dist/development/dom-export.mjs", "file": "react-router_dom.js", "fileHash": "0d081bed", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "e254f3af", "needsInterop": false}, "@emotion/react": {"src": "../../@emotion/react/dist/emotion-react.browser.development.esm.js", "file": "@emotion_react.js", "fileHash": "835e28f7", "needsInterop": false}, "@radix-ui/themes": {"src": "../../@radix-ui/themes/dist/esm/index.js", "file": "@radix-ui_themes.js", "fileHash": "3c9027b3", "needsInterop": false}, "json5": {"src": "../../json5/dist/index.mjs", "file": "json5.js", "fileHash": "0a21908b", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "7cfc508e", "needsInterop": false}, "react-error-boundary": {"src": "../../react-error-boundary/dist/react-error-boundary.development.js", "file": "react-error-boundary.js", "fileHash": "2429b836", "needsInterop": false}, "socket.io-client": {"src": "../../socket.io-client/build/esm/index.js", "file": "socket__io-client.js", "fileHash": "c3df8038", "needsInterop": false}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "fecaa2b1", "needsInterop": false}, "universal-cookie": {"src": "../../universal-cookie/esm/index.mjs", "file": "universal-cookie.js", "fileHash": "63414888", "needsInterop": false}}, "chunks": {"chunk-MJNCUEZK": {"file": "chunk-MJNCUEZK.js"}, "chunk-I677GNDM": {"file": "chunk-I677GNDM.js"}, "chunk-BKRNGYZE": {"file": "chunk-BKRNGYZE.js"}, "chunk-HE4GKDYE": {"file": "chunk-HE4GKDYE.js"}, "chunk-QFSPL5UL": {"file": "chunk-QFSPL5UL.js"}, "chunk-UGC3UZ7L": {"file": "chunk-UGC3UZ7L.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}