"""
Psychiatry EMR - Clinical Assessment Models
Database models for clinical assessments and DSM-5-TR integration.
"""

from __future__ import annotations
import reflex as rx
from sqlmodel import SQLModel, Field, Relationship
from datetime import datetime, date
from typing import Optional, Dict, Any
from pydantic import BaseModel
import json

class PresentIllnessData(BaseModel):
    """Pydantic model for present illness validation"""
    id: Optional[int] = None
    patient_id: int
    assessment_date: date
    chief_complaint: str
    history_present_illness: str
    primary_diagnosis: Optional[str] = None
    secondary_diagnoses: Optional[str] = None
    dsm5_criteria_met: Optional[Dict[str, Any]] = None
    treatment_plan: Optional[str] = None
    created_at: Optional[datetime] = None

class PresentIllness(SQLModel, table=True):
    """Present illness documentation with DSM-5-TR integration"""
    id: Optional[int] = Field(default=None, primary_key=True)
    patient_id: int = Field(foreign_key="patient.id")
    assessment_date: date = Field(index=True)
    chief_complaint: str
    history_present_illness: str
    primary_diagnosis: Optional[str] = Field(default=None)
    secondary_diagnoses: Optional[str] = Field(default=None)
    dsm5_criteria_json: Optional[str] = Field(default=None)  # JSON string for criteria results
    treatment_plan: Optional[str] = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_by: int
    
    # Relationships
    patient: Optional["Patient"] = Relationship(back_populates="clinical_assessments")
    
    @property
    def dsm5_criteria_met(self) -> Optional[Dict[str, Any]]:
        """Parse DSM-5 criteria from JSON string"""
        if self.dsm5_criteria_json:
            return json.loads(self.dsm5_criteria_json)
        return None
    
    @dsm5_criteria_met.setter
    def dsm5_criteria_met(self, value: Optional[Dict[str, Any]]) -> None:
        """Store DSM-5 criteria as JSON string"""
        self.dsm5_criteria_json = json.dumps(value) if value else None
