{"name": "reflex", "type": "module", "scripts": {"dev": "react-router dev --host", "export": "react-router build", "prod": "serve ./build/client"}, "dependencies": {"@radix-ui/themes": "3.2.1", "@react-router/node": "7.6.3", "isbot": "5.1.28", "json5": "2.2.3", "lucide-react": "0.525.0", "react": "19.1.0", "react-debounce-input": "3.3.0", "react-dom": "19.1.0", "react-error-boundary": "6.0.0", "react-helmet": "6.1.0", "react-router": "7.6.3", "react-router-dom": "7.6.3", "serve": "14.2.4", "socket.io-client": "4.8.1", "sonner": "2.0.6", "universal-cookie": "7.2.2"}, "devDependencies": {"@emotion/react": "11.14.0", "autoprefixer": "10.4.21", "postcss": "8.5.6", "postcss-import": "16.1.1", "@react-router/dev": "7.6.3", "@react-router/fs-routes": "7.6.3", "rolldown-vite": "7.0.9"}, "overrides": {"react-is": "19.1.0", "cookie": "1.0.2", "rollup": "4.44.2"}}