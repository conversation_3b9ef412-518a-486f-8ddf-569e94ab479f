"""
Psychiatry EMR - Clinical Assessment Page
Assessment forms, DSM-5 integration, and treatment planning.
"""

import reflex as rx
from datetime import date

from states.clinical_state import ClinicalState
from states.patient_state import PatientState
from states.auth_state import AuthState
from components.dsm5_forms import (
    disorder_selection_component,
    criteria_evaluation_form,
    dsm5_action_buttons
)

def clinical_assessment_page() -> rx.Component:
    """Clinical assessment page with comprehensive forms"""
    return rx.vstack(
        # Page header
        assessment_page_header(),
        
        # Main content
        rx.container(
            rx.vstack(
                # Patient selection section
                patient_selection_section(),
                
                # Assessment form sections
                rx.cond(
                    ClinicalState.assessment_form["patient_id"] != 0,
                    rx.vstack(
                        # Basic assessment information
                        basic_assessment_section(),
                        
                        # Clinical history section
                        clinical_history_section(),
                        
                        # DSM-5-TR diagnostic section
                        dsm5_diagnostic_section(),
                        
                        # Treatment planning section
                        treatment_planning_section(),
                        
                        # Assessment actions
                        assessment_actions_section(),
                        
                        spacing="6",
                        width="100%"
                    ),
                    # No patient selected message
                    no_patient_selected_component()
                ),
                
                spacing="6",
                width="100%"
            ),
            max_width="1200px",
            p=6
        ),
        
        width="100%",
        min_height="100vh",
        bg="gray.50"
    )

def assessment_page_header() -> rx.Component:
    """Assessment page header"""
    return rx.box(
        rx.container(
            rx.hstack(
                # Breadcrumb navigation
                rx.hstack(
                    rx.link("Dashboard", href="/dashboard", color="blue.600"),
                    rx.text("/", color="gray.400"),
                    rx.text("Clinical Assessment", font_weight="bold"),
                    spacing="2"
                ),
                
                rx.spacer(),
                
                # Quick actions
                rx.hstack(
                    rx.button(
                        "Load Assessment",
                        variant="outline",
                        size="sm"
                    ),
                    rx.button(
                        "Save Draft",
                        color_scheme="gray",
                        size="sm"
                    ),
                    spacing=2
                ),
                
                width="100%",
                align="center"
            ),
            max_width="1200px"
        ),
        width="100%",
        bg="white",
        border_bottom="1px solid",
        border_color="gray.200",
        p=4
    )

def patient_selection_section() -> rx.Component:
    """Patient selection for assessment"""
    return rx.card(
        rx.vstack(
            rx.heading("Patient Selection", size="md", mb=3),
            
            rx.cond(
                PatientState.current_patient,
                # Selected patient display
                rx.hstack(
                    rx.vstack(
                        rx.text(f"{PatientState.current_patient.first_name} {PatientState.current_patient.last_name}", font_weight="bold", font_size="lg"),
                        rx.text(f"DOB: {PatientState.current_patient.date_of_birth.strftime('%Y-%m-%d') if PatientState.current_patient.date_of_birth else 'Not provided'}", color="gray.600"),
                        rx.text(f"ID: {PatientState.current_patient.id}", color="gray.600"),
                        spacing=1,
                        align="start"
                    ),
                    rx.spacer(),
                    rx.button(
                        "Change Patient",
                        variant="outline",
                        size="sm"
                    ),
                    width="100%",
                    align="center"
                ),
                # Patient search/selection
                rx.vstack(
                    rx.text("Search and select a patient to begin assessment:", color="gray.600"),
                    rx.hstack(
                        rx.input(
                            placeholder="Search patients by name or ID...",
                            value=PatientState.search_term,
                            on_change=PatientState.set_search_term,
                            width="100%"
                        ),
                        rx.button(
                            "Search",
                            on_click=PatientState.search_patients,
                            color_scheme="blue"
                        ),
                        width="100%",
                        spacing=2
                    ),
                    
                    # Quick search results
                    rx.cond(
                        PatientState.search_results,
                        rx.vstack(
                            rx.text("Select a patient:", font_weight="bold"),
                            rx.foreach(
                                PatientState.search_results[:5],  # Show first 5 results
                                lambda patient: rx.button(
                                    f"{patient.first_name} {patient.last_name} (ID: {patient.id})",
                                    on_click=lambda: [
                                        PatientState.load_patient(patient.id),
                                        ClinicalState.update_assessment_form("patient_id", patient.id)
                                    ],
                                    variant="outline",
                                    width="100%",
                                    justify="start"
                                )
                            ),
                            spacing=2,
                            width="100%"
                        )
                    ),
                    
                    spacing=3,
                    width="100%"
                )
            ),
            
            spacing=3,
            width="100%"
        ),
        p=4,
        width="100%"
    )

def basic_assessment_section() -> rx.Component:
    """Basic assessment information form"""
    return rx.card(
        rx.vstack(
            rx.heading("Assessment Information", size="md", mb=4),
            
            rx.grid(
                # Assessment date
                rx.vstack(
                    rx.text("Assessment Date *", font_weight="bold"),
                    rx.input(
                        type="date",
                        value=ClinicalState.assessment_form["assessment_date"],
                        on_change=lambda value: ClinicalState.update_assessment_form("assessment_date", value),
                        width="100%"
                    ),
                    spacing=2,
                    align="start"
                ),
                
                # Assessment type
                rx.vstack(
                    rx.text("Assessment Type", font_weight="bold"),
                    rx.select(
                        ["Initial Evaluation", "Follow-up", "Crisis Assessment", "Medication Review", "Discharge Planning"],
                        placeholder="Select assessment type",
                        width="100%"
                    ),
                    spacing=2,
                    align="start"
                ),
                
                columns="2",
                spacing="4",
                width="100%"
            ),
            
            # Chief complaint
            rx.vstack(
                rx.text("Chief Complaint *", font_weight="bold"),
                rx.textarea(
                    placeholder="Patient's primary concern or reason for visit...",
                    value=ClinicalState.assessment_form["chief_complaint"],
                    on_change=lambda value: ClinicalState.update_assessment_form("chief_complaint", value),
                    width="100%",
                    min_height="100px"
                ),
                spacing=2,
                align="start",
                width="100%"
            ),
            
            spacing=4,
            width="100%"
        ),
        p=4,
        width="100%"
    )

def clinical_history_section() -> rx.Component:
    """Clinical history and present illness"""
    return rx.card(
        rx.vstack(
            rx.heading("Clinical History", size="md", mb=4),
            
            # History of present illness
            rx.vstack(
                rx.text("History of Present Illness *", font_weight="bold"),
                rx.textarea(
                    placeholder="Detailed description of current symptoms, onset, duration, severity, and course...",
                    value=ClinicalState.assessment_form["history_present_illness"],
                    on_change=lambda value: ClinicalState.update_assessment_form("history_present_illness", value),
                    width="100%",
                    min_height="150px"
                ),
                spacing=2,
                align="start",
                width="100%"
            ),
            
            # Additional history sections
            rx.grid(
                rx.vstack(
                    rx.text("Past Psychiatric History", font_weight="bold"),
                    rx.textarea(
                        placeholder="Previous mental health diagnoses, treatments, hospitalizations...",
                        width="100%",
                        min_height="100px"
                    ),
                    spacing=2,
                    align="start"
                ),
                
                rx.vstack(
                    rx.text("Medical History", font_weight="bold"),
                    rx.textarea(
                        placeholder="Relevant medical conditions, medications, allergies...",
                        width="100%",
                        min_height="100px"
                    ),
                    spacing=2,
                    align="start"
                ),
                
                rx.vstack(
                    rx.text("Family History", font_weight="bold"),
                    rx.textarea(
                        placeholder="Family history of mental health or medical conditions...",
                        width="100%",
                        min_height="100px"
                    ),
                    spacing=2,
                    align="start"
                ),
                
                rx.vstack(
                    rx.text("Social History", font_weight="bold"),
                    rx.textarea(
                        placeholder="Substance use, relationships, work, education, trauma history...",
                        width="100%",
                        min_height="100px"
                    ),
                    spacing=2,
                    align="start"
                ),
                
                columns="2",
                spacing="4",
                width="100%"
            ),
            
            spacing=4,
            width="100%"
        ),
        p=4,
        width="100%"
    )

def dsm5_diagnostic_section() -> rx.Component:
    """DSM-5-TR diagnostic evaluation section"""
    return rx.card(
        rx.vstack(
            rx.heading("DSM-5-TR Diagnostic Evaluation", size="md", mb=4),
            
            # Disorder selection
            disorder_selection_component(),
            
            # Criteria evaluation form
            criteria_evaluation_form(),
            
            spacing=4,
            width="100%"
        ),
        p=4,
        width="100%"
    )

def treatment_planning_section() -> rx.Component:
    """Treatment planning section"""
    return rx.card(
        rx.vstack(
            rx.heading("Treatment Planning", size="md", mb=4),
            
            rx.grid(
                # Primary diagnosis
                rx.vstack(
                    rx.text("Primary Diagnosis", font_weight="bold"),
                    rx.input(
                        placeholder="Primary diagnostic impression...",
                        value=ClinicalState.assessment_form["primary_diagnosis"],
                        on_change=lambda value: ClinicalState.update_assessment_form("primary_diagnosis", value),
                        width="100%"
                    ),
                    spacing=2,
                    align="start"
                ),
                
                # Secondary diagnoses
                rx.vstack(
                    rx.text("Secondary Diagnoses", font_weight="bold"),
                    rx.input(
                        placeholder="Additional diagnoses or rule-outs...",
                        value=ClinicalState.assessment_form["secondary_diagnoses"],
                        on_change=lambda value: ClinicalState.update_assessment_form("secondary_diagnoses", value),
                        width="100%"
                    ),
                    spacing=2,
                    align="start"
                ),
                
                columns="2",
                spacing="4",
                width="100%"
            ),
            
            # Treatment plan
            rx.vstack(
                rx.text("Treatment Plan *", font_weight="bold"),
                rx.textarea(
                    placeholder="Comprehensive treatment plan including interventions, medications, therapy recommendations, follow-up plans...",
                    value=ClinicalState.assessment_form["treatment_plan"],
                    on_change=lambda value: ClinicalState.update_assessment_form("treatment_plan", value),
                    width="100%",
                    min_height="150px"
                ),
                spacing=2,
                align="start",
                width="100%"
            ),
            
            # Risk assessment
            rx.vstack(
                rx.text("Risk Assessment", font_weight="bold"),
                rx.textarea(
                    placeholder="Suicide risk, violence risk, safety planning...",
                    width="100%",
                    min_height="100px"
                ),
                spacing=2,
                align="start",
                width="100%"
            ),
            
            spacing=4,
            width="100%"
        ),
        p=4,
        width="100%"
    )

def assessment_actions_section() -> rx.Component:
    """Assessment action buttons"""
    return rx.card(
        rx.hstack(
            rx.button(
                "Save as Draft",
                color_scheme="gray",
                size="lg"
            ),
            rx.button(
                "Preview Assessment",
                color_scheme="blue",
                size="lg"
            ),
            rx.spacer(),
            rx.button(
                "Cancel",
                variant="outline",
                size="lg"
            ),
            rx.button(
                "Complete Assessment",
                on_click=ClinicalState.create_assessment,
                loading=ClinicalState.is_loading,
                color_scheme="green",
                size="lg"
            ),
            width="100%"
        ),
        p=4,
        width="100%"
    )

def no_patient_selected_component() -> rx.Component:
    """No patient selected state"""
    return rx.card(
        rx.vstack(
            rx.icon("user-search", size=48, color="gray.400"),
            rx.heading("No Patient Selected", size="md", color="gray.600"),
            rx.text("Please select a patient to begin the clinical assessment", color="gray.500"),
            rx.button(
                "Search Patients",
                color_scheme="blue",
                size="lg",
                mt=4
            ),
            spacing=3,
            align="center"
        ),
        p=8,
        width="100%",
        text_align="center"
    )
