

import { Fragment, useCallback, useContext, useEffect } from "react"
import { EventLoopContext, StateContexts } from "$/utils/context"
import { Event, isNotNullOrUndefined, isTrue } from "$/utils/state"
import { Badge as RadixThemesBadge, Box as RadixThemesBox, <PERSON>ton as RadixThemesButton, Card as RadixThemesCard, Checkbox as RadixThemesCheckbox, Container as RadixThemesContainer, DropdownMenu as RadixThemesDropdownMenu, Flex as RadixThemesFlex, Grid as RadixThemesGrid, Heading as RadixThemesHeading, Link as RadixThemesLink, Progress as RadixThemesProgress, Select as RadixThemesSelect, Text as RadixThemesText, TextArea as RadixThemesTextArea, TextField as RadixThemesTextField } from "@radix-ui/themes"
import { <PERSON><PERSON><PERSON> as LucideBarChart, BrainCircuit as LucideBrainCircuit, ChevronDown as LucideChevronDown, Circle as LucideCircle, CircleHelp as LucideCircleHelp, ClipboardList as LucideClipboardList, ClipboardPlus as LucideClipboardPlus, Home as LucideHome, Key as LucideKey, LogOut as LucideLogOut, Plus as LucidePlus, Settings as LucideSettings, User as LucideUser, Users as LucideUsers, UserSearch as LucideUserSearch } from "lucide-react"
import { Link as ReactRouterLink } from "react-router"
import DebounceInput from "react-debounce-input"
import { jsx } from "@emotion/react"



function Select__root_106738631248600186881312526823455161113 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_change_9075bba8ab1526c9cba7a2cbd3e163b3 = useCallback(((_ev_0) => (addEvents([(Event("reflex___state____state.states___clinical_state____clinical_state.select_disorder", ({ ["disorder_code"] : _ev_0 }), ({  })))], [_ev_0], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesSelect.Root,
{onValueChange:on_change_9075bba8ab1526c9cba7a2cbd3e163b3,size:"3",value:reflex___state____state__states___clinical_state____clinical_state.selected_disorder_rx_state_},
jsx(RadixThemesSelect.Trigger,{css:({ ["width"] : "100%" }),placeholder:"Choose a disorder to evaluate..."},)
,jsx(
RadixThemesSelect.Content,
{},
jsx(
RadixThemesSelect.Group,
{},
""
,jsx(
RadixThemesSelect.Item,
{value:"Major Depressive Disorder"},
"Major Depressive Disorder"
,),jsx(
RadixThemesSelect.Item,
{value:"Generalized Anxiety Disorder"},
"Generalized Anxiety Disorder"
,),jsx(
RadixThemesSelect.Item,
{value:"Bipolar I Disorder"},
"Bipolar I Disorder"
,),jsx(
RadixThemesSelect.Item,
{value:"Bipolar II Disorder"},
"Bipolar II Disorder"
,),jsx(
RadixThemesSelect.Item,
{value:"Panic Disorder"},
"Panic Disorder"
,),jsx(
RadixThemesSelect.Item,
{value:"Social Anxiety Disorder"},
"Social Anxiety Disorder"
,),jsx(
RadixThemesSelect.Item,
{value:"PTSD"},
"PTSD"
,),jsx(
RadixThemesSelect.Item,
{value:"ADHD"},
"ADHD"
,),jsx(
RadixThemesSelect.Item,
{value:"Autism Spectrum Disorder"},
"Autism Spectrum Disorder"
,),jsx(
RadixThemesSelect.Item,
{value:"Schizophrenia"},
"Schizophrenia"
,),),),)
  )
}

function Button_93614305574751035611651602300509813186 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_14984ba133eefccbbc7192e1aa59461b = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___patient_state____patient_state.search_patients", ({  }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{color:"blue",onClick:on_click_14984ba133eefccbbc7192e1aa59461b},
"Search"
,)
  )
}

function Debounceinput_216747197841777122544201187882581230925 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_change_0c7b9cb1e7c5dbea46e4621acbbbfa2c = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___clinical_state____clinical_state.update_assessment_form", ({ ["field"] : "chief_complaint", ["value"] : _e["target"]["value"] }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(DebounceInput,{css:({ ["width"] : "100%", ["minHeight"] : "100px" }),debounceTimeout:300,element:RadixThemesTextArea,onChange:on_change_0c7b9cb1e7c5dbea46e4621acbbbfa2c,placeholder:"Patient's primary concern or reason for visit...",value:reflex___state____state__states___clinical_state____clinical_state.assessment_form_rx_state_["chief_complaint"]},)

  )
}

function Fragment_44540825102101947695183164596470730595 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___clinical_state____clinical_state.error_message_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["position"] : "fixed", ["top"] : "80px", ["right"] : "20px", ["zIndex"] : "9999", ["maxWidth"] : "400px", ["background"] : "red.50", ["border"] : "1px solid", ["borderColor"] : "red.200" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircleHelp,{css:({ ["color"] : "red.500" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Error"
,),jsx(Text_31998917857994533249003073964684816120,{},)
,),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Fragment_291002643453393394795517824736366687605 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
Fragment,
{},
(reflex___state____state__states___clinical_state____clinical_state.disorder_criteria_rx_state_?.["duration_required"] ? (jsx(
Fragment,
{},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "orange.600", ["fontStyle"] : "italic" })},
"Duration requirements apply"
,),)) : (jsx(Fragment,{},)
)),)
  )
}

function Fragment_44837027506772755226363404212819306791 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___clinical_state____clinical_state.dsm5_evaluation_rx_state_?.["met_criteria_ids"]) ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold", ["color"] : "green.700" })},
"Met Criteria:"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "green.600" })},
"\u2022 Criteria evaluation in progress..."
,),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Badge_176058538072098687603084575584034382682 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesBadge,
{color:((reflex___state____state__states___clinical_state____clinical_state.diagnosis_confidence_rx_state_ === "High confidence") ? "green" : ((reflex___state____state__states___clinical_state____clinical_state.diagnosis_confidence_rx_state_ === "Moderate confidence") ? "yellow" : "red"))},
reflex___state____state__states___clinical_state____clinical_state.diagnosis_confidence_rx_state_
,)
  )
}

function Progress_40759411031450424366480919784502127065 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(RadixThemesProgress,{color:"blue",css:({ ["width"] : "100%" }),size:"3",value:reflex___state____state__states___clinical_state____clinical_state.criteria_met_percentage_rx_state_},)

  )
}

function Fragment_54473482700804687792538982374946944731 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___patient_state____patient_state.current_patient_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(Text_3043557093842810840467751045346406345,{},)
,jsx(Text_313965121980351552708157993177867214648,{},)
,jsx(Text_258119496400724759000815939262539373656,{},)
,),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesButton,
{size:"2",variant:"outline"},
"Change Patient"
,),),)) : (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"3"},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600" })},
"Search and select a patient to begin assessment:"
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"2"},
jsx(Debounceinput_272681826588481588596473780023064890749,{},)
,jsx(Button_93614305574751035611651602300509813186,{},)
,),jsx(Fragment_12240279494810518390119348895102139998,{},)
,),))),)
  )
}

function Text_313965121980351552708157993177867214648 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600" })},
("DOB: "+reflex___state____state__states___patient_state____patient_state.current_patient_rx_state_?.["dob"])
,)
  )
}

function Fragment_234769145907334553952114246839368203877 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
Fragment,
{},
(!((reflex___state____state__states___clinical_state____clinical_state.assessment_form_rx_state_["patient_id"] === 0)) ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"6"},
jsx(
RadixThemesCard,
{css:({ ["p"] : 4, ["width"] : "100%" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"4"},
jsx(
RadixThemesHeading,
{css:({ ["mb"] : 4 }),size:"5"},
"Assessment Information"
,),jsx(
RadixThemesGrid,
{columns:"2",css:({ ["width"] : "100%" }),gap:"4"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Assessment Date *"
,),jsx(Debounceinput_262562937531728043971569551798756721694,{},)
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Assessment Type"
,),jsx(
RadixThemesSelect.Root,
{},
jsx(RadixThemesSelect.Trigger,{css:({ ["width"] : "100%" }),placeholder:"Select assessment type"},)
,jsx(
RadixThemesSelect.Content,
{},
jsx(
RadixThemesSelect.Group,
{},
""
,jsx(
RadixThemesSelect.Item,
{value:"Initial Evaluation"},
"Initial Evaluation"
,),jsx(
RadixThemesSelect.Item,
{value:"Follow-up"},
"Follow-up"
,),jsx(
RadixThemesSelect.Item,
{value:"Crisis Assessment"},
"Crisis Assessment"
,),jsx(
RadixThemesSelect.Item,
{value:"Medication Review"},
"Medication Review"
,),jsx(
RadixThemesSelect.Item,
{value:"Discharge Planning"},
"Discharge Planning"
,),),),),),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Chief Complaint *"
,),jsx(Debounceinput_216747197841777122544201187882581230925,{},)
,),),),jsx(
RadixThemesCard,
{css:({ ["p"] : 4, ["width"] : "100%" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"4"},
jsx(
RadixThemesHeading,
{css:({ ["mb"] : 4 }),size:"5"},
"Clinical History"
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"History of Present Illness *"
,),jsx(Debounceinput_167766086830985829604638901006054832927,{},)
,),jsx(
RadixThemesGrid,
{columns:"2",css:({ ["width"] : "100%" }),gap:"4"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Past Psychiatric History"
,),jsx(RadixThemesTextArea,{css:({ ["& textarea"] : null, ["width"] : "100%", ["minHeight"] : "100px" }),placeholder:"Previous mental health diagnoses, treatments, hospitalizations..."},)
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Medical History"
,),jsx(RadixThemesTextArea,{css:({ ["& textarea"] : null, ["width"] : "100%", ["minHeight"] : "100px" }),placeholder:"Relevant medical conditions, medications, allergies..."},)
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Family History"
,),jsx(RadixThemesTextArea,{css:({ ["& textarea"] : null, ["width"] : "100%", ["minHeight"] : "100px" }),placeholder:"Family history of mental health or medical conditions..."},)
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Social History"
,),jsx(RadixThemesTextArea,{css:({ ["& textarea"] : null, ["width"] : "100%", ["minHeight"] : "100px" }),placeholder:"Substance use, relationships, work, education, trauma history..."},)
,),),),),jsx(
RadixThemesCard,
{css:({ ["p"] : 4, ["width"] : "100%" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"4"},
jsx(
RadixThemesHeading,
{css:({ ["mb"] : 4 }),size:"5"},
"DSM-5-TR Diagnostic Evaluation"
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"4"},
jsx(
RadixThemesHeading,
{css:({ ["mb"] : 4 }),size:"6"},
"DSM-5-TR Diagnostic Criteria"
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold", ["mb"] : 2 })},
"Select Disorder"
,),jsx(Select__root_106738631248600186881312526823455161113,{},)
,),jsx(Fragment_51313515439218957986554776414200497076,{},)
,),jsx(Fragment_66180594422720524184428071614418237441,{},)
,),),jsx(
RadixThemesCard,
{css:({ ["p"] : 4, ["width"] : "100%" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"4"},
jsx(
RadixThemesHeading,
{css:({ ["mb"] : 4 }),size:"5"},
"Treatment Planning"
,),jsx(
RadixThemesGrid,
{columns:"2",css:({ ["width"] : "100%" }),gap:"4"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Primary Diagnosis"
,),jsx(Debounceinput_150762359028084640014003087585971143196,{},)
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Secondary Diagnoses"
,),jsx(Debounceinput_237941045574519479377086684339777230564,{},)
,),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Treatment Plan *"
,),jsx(Debounceinput_66180745555598472261128921387021284883,{},)
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Risk Assessment"
,),jsx(RadixThemesTextArea,{css:({ ["& textarea"] : null, ["width"] : "100%", ["minHeight"] : "100px" }),placeholder:"Suicide risk, violence risk, safety planning..."},)
,),),),jsx(
RadixThemesCard,
{css:({ ["p"] : 4, ["width"] : "100%" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesButton,
{color:"gray",size:"3"},
"Save as Draft"
,),jsx(
RadixThemesButton,
{color:"blue",size:"3"},
"Preview Assessment"
,),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesButton,
{size:"3",variant:"outline"},
"Cancel"
,),jsx(Button_338700933386685087323273182333731322693,{},)
,),),),)) : (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["p"] : 8, ["width"] : "100%", ["textAlign"] : "center" })},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",direction:"column",gap:"3"},
jsx(LucideUserSearch,{css:({ ["color"] : "gray.400" }),size:48},)
,jsx(
RadixThemesHeading,
{css:({ ["color"] : "gray.600" }),size:"5"},
"No Patient Selected"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.500" })},
"Please select a patient to begin the clinical assessment"
,),jsx(
RadixThemesButton,
{color:"blue",css:({ ["mt"] : 4 }),size:"3"},
"Search Patients"
,),),),))),)
  )
}

function Text_102820030196061442617916723669586461230 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
reflex___state____state__states___clinical_state____clinical_state.success_message_rx_state_
,)
  )
}

function Text_179648672776200129942498047795827996331 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
("Minimum criteria required: "+reflex___state____state__states___clinical_state____clinical_state.disorder_criteria_rx_state_?.["minimum_criteria"])
,)
  )
}

function Fragment_240514998957387502309164644453271310886 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___patient_state____patient_state.success_message_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["position"] : "fixed", ["top"] : "20px", ["right"] : "20px", ["zIndex"] : "9999", ["maxWidth"] : "400px", ["background"] : "green.50", ["border"] : "1px solid", ["borderColor"] : "green.200" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircleHelp,{css:({ ["color"] : "green.500" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Success"
,),jsx(Text_48720832473938332111969035618750431193,{},)
,),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Fragment_192633857668537415374399445980174592197 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___patient_state____patient_state.error_message_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["position"] : "fixed", ["top"] : "20px", ["right"] : "20px", ["zIndex"] : "9999", ["maxWidth"] : "400px", ["background"] : "red.50", ["border"] : "1px solid", ["borderColor"] : "red.200" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircleHelp,{css:({ ["color"] : "red.500" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Error"
,),jsx(Text_230921353667065509099663485155314641824,{},)
,),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Text_145302368150489342538234271523397292048 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
("Met: "+reflex___state____state__states___clinical_state____clinical_state.dsm5_evaluation_rx_state_?.["met_criteria_count"])
,)
  )
}

function Text_230921353667065509099663485155314641824 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
reflex___state____state__states___patient_state____patient_state.error_message_rx_state_
,)
  )
}

function Text_239596050178459519438495520348781236428 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
("Required: "+reflex___state____state__states___clinical_state____clinical_state.dsm5_evaluation_rx_state_?.["required_criteria"])
,)
  )
}

function Debounceinput_66180745555598472261128921387021284883 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_change_218befda492d0bfbe0d43258b1527222 = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___clinical_state____clinical_state.update_assessment_form", ({ ["field"] : "treatment_plan", ["value"] : _e["target"]["value"] }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(DebounceInput,{css:({ ["width"] : "100%", ["minHeight"] : "150px" }),debounceTimeout:300,element:RadixThemesTextArea,onChange:on_change_218befda492d0bfbe0d43258b1527222,placeholder:"Comprehensive treatment plan including interventions, medications, therapy recommendations, follow-up plans...",value:reflex___state____state__states___clinical_state____clinical_state.assessment_form_rx_state_["treatment_plan"]},)

  )
}

function Debounceinput_167766086830985829604638901006054832927 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_change_8e4a58183252aedde42cd1679fb463a5 = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___clinical_state____clinical_state.update_assessment_form", ({ ["field"] : "history_present_illness", ["value"] : _e["target"]["value"] }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(DebounceInput,{css:({ ["width"] : "100%", ["minHeight"] : "150px" }),debounceTimeout:300,element:RadixThemesTextArea,onChange:on_change_8e4a58183252aedde42cd1679fb463a5,placeholder:"Detailed description of current symptoms, onset, duration, severity, and course...",value:reflex___state____state__states___clinical_state____clinical_state.assessment_form_rx_state_["history_present_illness"]},)

  )
}

function Fragment_12240279494810518390119348895102139998 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___patient_state____patient_state.search_results_rx_state_) ? (jsx(
Fragment,
{},
jsx(Flex_326358899339152429244191592959362492849,{},)
,)) : (jsx(Fragment,{},)
)),)
  )
}

function Fragment_51313515439218957986554776414200497076 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
Fragment,
{},
((reflex___state____state__states___clinical_state____clinical_state.available_disorders_rx_state_.length === 0) ? (jsx(
Fragment,
{},
jsx(Button_220065695165827451666909820248871337779,{},)
,)) : (jsx(Fragment,{},)
)),)
  )
}

function Text_30418560931415594894753637272072919251 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
("Confidence: "+(isTrue(reflex___state____state__states___clinical_state____clinical_state.dsm5_evaluation_rx_state_?.["confidence_level"]) ? reflex___state____state__states___clinical_state____clinical_state.dsm5_evaluation_rx_state_?.["confidence_level"] : 0))
,)
  )
}

function Text_124410050105615155824358786319773704031 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600" })},
("Code: "+reflex___state____state__states___clinical_state____clinical_state.disorder_criteria_rx_state_?.["disorder_code"])
,)
  )
}

function Text_258119496400724759000815939262539373656 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600" })},
("ID: "+reflex___state____state__states___patient_state____patient_state.current_patient_rx_state_?.["id"])
,)
  )
}

function Heading_156982111666106264475202724075184048066 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesHeading,
{size:"5"},
reflex___state____state__states___clinical_state____clinical_state.disorder_criteria_rx_state_?.["name"]
,)
  )
}

function Fragment_166734300427242336168950855614206449165 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
Fragment,
{},
(reflex___state____state__states___auth_state____auth_state.is_admin_rx_state_ ? (jsx(
Fragment,
{},
jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.700", ["fontWeight"] : "medium", ["&:hover"] : ({ ["color"] : "blue.600" }), ["&:active"] : ({ ["color"] : "blue.600", ["fontWeight"] : "bold" }) })},
jsx(
ReactRouterLink,
{to:"/admin"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideSettings,{size:18},)
,jsx(
RadixThemesText,
{as:"p"},
"Admin"
,),),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Debounceinput_272681826588481588596473780023064890749 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_change_624ca22a2dbf18bfd31c2f92729a1e9b = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___patient_state____patient_state.set_search_term", ({ ["value"] : _e["target"]["value"] }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(DebounceInput,{css:({ ["width"] : "100%" }),debounceTimeout:300,element:RadixThemesTextField.Root,onChange:on_change_624ca22a2dbf18bfd31c2f92729a1e9b,placeholder:"Search patients by name or ID...",value:(isNotNullOrUndefined(reflex___state____state__states___patient_state____patient_state.search_term_rx_state_) ? reflex___state____state__states___patient_state____patient_state.search_term_rx_state_ : "")},)

  )
}

function Flex_326358899339152429244191592959362492849 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);





  
  return (
    jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Select a patient:"
,),reflex___state____state__states___patient_state____patient_state.search_results_rx_state_.slice(undefined, 5).map((patient_rx_state_,index_6bc5144ba092794b)=>(jsx(
RadixThemesButton,
{css:({ ["width"] : "100%", ["justify"] : "start" }),key:index_6bc5144ba092794b,onClick:((_e) => (addEvents([(Event("reflex___state____state.states___patient_state____patient_state.load_patient", ({ ["patient_id"] : patient_rx_state_["id"] }), ({  }))), (Event("reflex___state____state.states___clinical_state____clinical_state.update_assessment_form", ({ ["field"] : "patient_id", ["value"] : patient_rx_state_["id"] }), ({  })))], [_e], ({  })))),variant:"outline"},
(patient_rx_state_["name"]+" (ID: "+patient_rx_state_["id"]+")")
,))),)
  )
}

function Button_220065695165827451666909820248871337779 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_f4f90e23637b71f5376f9d927d66ed3c = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___clinical_state____clinical_state.load_disorders", ({  }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{color:"blue",onClick:on_click_f4f90e23637b71f5376f9d927d66ed3c,size:"3"},
"Load Available Disorders"
,)
  )
}

function Debounceinput_150762359028084640014003087585971143196 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_change_af02f420b6fd3a3831ab0fc0ee8b5747 = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___clinical_state____clinical_state.update_assessment_form", ({ ["field"] : "primary_diagnosis", ["value"] : _e["target"]["value"] }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(DebounceInput,{css:({ ["width"] : "100%" }),debounceTimeout:300,element:RadixThemesTextField.Root,onChange:on_change_af02f420b6fd3a3831ab0fc0ee8b5747,placeholder:"Primary diagnostic impression...",value:(isNotNullOrUndefined(reflex___state____state__states___clinical_state____clinical_state.assessment_form_rx_state_["primary_diagnosis"]) ? reflex___state____state__states___clinical_state____clinical_state.assessment_form_rx_state_["primary_diagnosis"] : "")},)

  )
}

function Text_240095476290627264844530266124211513421 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
("Total: "+Object.keys(reflex___state____state__states___clinical_state____clinical_state.criterion_responses_rx_state_).length)
,)
  )
}

function Fragment_7277909197666096817838437013620370764 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
Fragment,
{},
(reflex___state____state__states___auth_state____auth_state.is_authenticated_rx_state_ ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",direction:"row",gap:"2"},
jsx(
RadixThemesButton,
{size:"2",title:"New Patient",variant:"ghost"},
jsx(LucidePlus,{size:16},)
,),jsx(
RadixThemesButton,
{size:"2",title:"New Assessment",variant:"ghost"},
jsx(LucideClipboardPlus,{size:16},)
,),jsx(
RadixThemesDropdownMenu.Root,
{},
jsx(Dropdownmenu__trigger_147570026540717198363334525227418462761,{},)
,jsx(
RadixThemesDropdownMenu.Content,
{},
jsx(
RadixThemesDropdownMenu.Item,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideUser,{size:16},)
,jsx(
RadixThemesText,
{as:"p"},
"Profile"
,),),),jsx(
RadixThemesDropdownMenu.Item,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideKey,{size:16},)
,jsx(
RadixThemesText,
{as:"p"},
"Change Password"
,),),),jsx(
RadixThemesDropdownMenu.Item,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideSettings,{size:16},)
,jsx(
RadixThemesText,
{as:"p"},
"Settings"
,),),),jsx(RadixThemesDropdownMenu.Separator,{},)
,jsx(Dropdownmenu__item_53865495627125640446257350871203294164,{},)
,),),),)) : (jsx(
Fragment,
{},
jsx(Button_213432182251270348700044919999605871278,{},)
,))),)
  )
}

function Button_213432182251270348700044919999605871278 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_e702a5da1364a6fc9edb845ecb9a4cfa = useCallback(((_e) => (addEvents([(Event("_redirect", ({ ["path"] : "/login", ["external"] : false, ["replace"] : false }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{color:"blue",onClick:on_click_e702a5da1364a6fc9edb845ecb9a4cfa,size:"2"},
"Login"
,)
  )
}

function Flex_331207753910449632678560541778733323758 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);





  
  return (
    jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%", ["p"] : 4, ["border"] : "1px solid", ["borderColor"] : "gray.200", ["borderRadius"] : "md" }),direction:"column",gap:"3"},
jsx(
RadixThemesHeading,
{css:({ ["mb"] : 3 }),size:"5"},
"Diagnostic Criteria"
,),reflex___state____state__states___clinical_state____clinical_state.disorder_criteria_rx_state_?.["criteria"].map((criterion_rx_state_,index_3b73f4ec61a91ff7)=>(jsx(
RadixThemesCard,
{css:({ ["width"] : "100%", ["p"] : 3, ["&:hover"] : ({ ["background"] : "gray.50" }) }),key:index_3b73f4ec61a91ff7},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesText,
{as:"label",size:"3"},
jsx(
RadixThemesFlex,
{gap:"2"},
jsx(RadixThemesCheckbox,{checked:(reflex___state____state__states___clinical_state____clinical_state.criterion_responses_rx_state_[criterion_rx_state_["id"]] ? reflex___state____state__states___clinical_state____clinical_state.criterion_responses_rx_state_[criterion_rx_state_["id"]] : false),onCheckedChange:((_ev_0) => (addEvents([(Event("reflex___state____state.states___clinical_state____clinical_state.update_criterion_response", ({ ["criterion_id"] : criterion_rx_state_["id"], ["value"] : _ev_0 }), ({  })))], [_ev_0], ({  })))),size:"3"},)
,""
,),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["flex"] : "1" }),direction:"column",gap:"1"},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",direction:"row",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "medium" })},
criterion_rx_state_["description"]
,),jsx(
Fragment,
{},
(criterion_rx_state_["required"] ? (jsx(
Fragment,
{},
jsx(
RadixThemesBadge,
{color:"red",size:"1"},
"Required"
,),)) : (jsx(Fragment,{},)
)),),),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.500", ["fontSize"] : "sm" })},
("Criterion ID: "+criterion_rx_state_["id"])
,),),jsx(
Fragment,
{},
((reflex___state____state__states___clinical_state____clinical_state.criterion_responses_rx_state_[criterion_rx_state_["id"]] ? reflex___state____state__states___clinical_state____clinical_state.criterion_responses_rx_state_[criterion_rx_state_["id"]] : false) ? (jsx(
Fragment,
{},
jsx(LucideCircleHelp,{css:({ ["color"] : "green.500" }),size:20},)
,)) : (jsx(
Fragment,
{},
jsx(LucideCircle,{css:({ ["color"] : "gray.300" }),size:20},)
,))),),),))),)
  )
}

function Text_31998917857994533249003073964684816120 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
reflex___state____state__states___clinical_state____clinical_state.error_message_rx_state_
,)
  )
}

function Fragment_72192638570632301994735261530527745264 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___clinical_state____clinical_state.success_message_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["position"] : "fixed", ["top"] : "80px", ["right"] : "20px", ["zIndex"] : "9999", ["maxWidth"] : "400px", ["background"] : "green.50", ["border"] : "1px solid", ["borderColor"] : "green.200" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircleHelp,{css:({ ["color"] : "green.500" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Success"
,),jsx(Text_102820030196061442617916723669586461230,{},)
,),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Fragment_216061012743779624452318208511252989372 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
Fragment,
{},
(reflex___state____state__states___auth_state____auth_state.is_authenticated_rx_state_ ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%", ["minHeight"] : "100vh" }),direction:"column",gap:"0"},
jsx(
RadixThemesBox,
{css:({ ["width"] : "100%", ["background"] : "white", ["borderBottom"] : "1px solid", ["borderColor"] : "gray.200", ["p"] : 4, ["position"] : "sticky", ["top"] : "0", ["zIndex"] : "1000" })},
jsx(
RadixThemesContainer,
{css:({ ["padding"] : "16px", ["maxWidth"] : "1200px" }),size:"3"},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"3"},
jsx(LucideBrainCircuit,{css:({ ["color"] : "blue.600" }),size:32},)
,jsx(
RadixThemesHeading,
{css:({ ["color"] : "blue.600" }),size:"6"},
"Psychiatry EMR"
,),),jsx(Fragment_252584447231808486301477474528160783206,{},)
,jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(Fragment_7277909197666096817838437013620370764,{},)
,),),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(Fragment_240514998957387502309164644453271310886,{},)
,jsx(Fragment_192633857668537415374399445980174592197,{},)
,jsx(Fragment_72192638570632301994735261530527745264,{},)
,jsx(Fragment_44540825102101947695183164596470730595,{},)
,),jsx(
RadixThemesBox,
{css:({ ["width"] : "100%", ["flex"] : "1" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%", ["minHeight"] : "100vh", ["background"] : "gray.50" }),direction:"column",gap:"3"},
jsx(
RadixThemesBox,
{css:({ ["width"] : "100%", ["background"] : "white", ["borderBottom"] : "1px solid", ["borderColor"] : "gray.200", ["p"] : 4 })},
jsx(
RadixThemesContainer,
{css:({ ["padding"] : "16px", ["maxWidth"] : "1200px" }),size:"3"},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "blue.600", ["&:hover"] : ({ ["color"] : "var(--accent-8)" }) })},
jsx(
ReactRouterLink,
{to:"/dashboard"},
"Dashboard"
,),),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.400" })},
"/"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Clinical Assessment"
,),),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(
RadixThemesButton,
{size:"2",variant:"outline"},
"Load Assessment"
,),jsx(
RadixThemesButton,
{color:"gray",size:"2"},
"Save Draft"
,),),),),),jsx(
RadixThemesContainer,
{css:({ ["padding"] : "16px", ["maxWidth"] : "1200px", ["p"] : 6 }),size:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"6"},
jsx(
RadixThemesCard,
{css:({ ["p"] : 4, ["width"] : "100%" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"3"},
jsx(
RadixThemesHeading,
{css:({ ["mb"] : 3 }),size:"5"},
"Patient Selection"
,),jsx(Fragment_54473482700804687792538982374946944731,{},)
,),),jsx(Fragment_234769145907334553952114246839368203877,{},)
,),),),),jsx(
RadixThemesBox,
{css:({ ["width"] : "100%", ["background"] : "gray.100", ["borderTop"] : "1px solid", ["borderColor"] : "gray.200", ["p"] : 4, ["mt"] : "auto" })},
jsx(
RadixThemesContainer,
{css:({ ["padding"] : "16px", ["maxWidth"] : "1200px" }),size:"3"},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600", ["fontSize"] : "sm" })},
"\u00a9 2024 Psychiatry EMR. All rights reserved."
,),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.500", ["fontSize"] : "xs" })},
"Version 1.0.0"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.400" })},
"\u2022"
,),jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.500", ["fontSize"] : "xs", ["&:hover"] : ({ ["color"] : "var(--accent-8)" }) })},
jsx(
ReactRouterLink,
{to:"/privacy"},
"Privacy Policy"
,),),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.400" })},
"\u2022"
,),jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.500", ["fontSize"] : "xs", ["&:hover"] : ({ ["color"] : "var(--accent-8)" }) })},
jsx(
ReactRouterLink,
{to:"/support"},
"Support"
,),),),),),),),)) : (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["p"] : 8 }),direction:"column",gap:"4"},
jsx(
RadixThemesHeading,
{size:"6"},
"Authentication Required"
,),jsx(
RadixThemesText,
{as:"p"},
"Please log in to access this page."
,),jsx(Button_169816151628875222609663720048451338759,{},)
,),))),)
  )
}

function Debounceinput_237941045574519479377086684339777230564 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_change_e9ae33a364429cb42411a87e8e34728f = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___clinical_state____clinical_state.update_assessment_form", ({ ["field"] : "secondary_diagnoses", ["value"] : _e["target"]["value"] }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(DebounceInput,{css:({ ["width"] : "100%" }),debounceTimeout:300,element:RadixThemesTextField.Root,onChange:on_change_e9ae33a364429cb42411a87e8e34728f,placeholder:"Additional diagnoses or rule-outs...",value:(isNotNullOrUndefined(reflex___state____state__states___clinical_state____clinical_state.assessment_form_rx_state_["secondary_diagnoses"]) ? reflex___state____state__states___clinical_state____clinical_state.assessment_form_rx_state_["secondary_diagnoses"] : "")},)

  )
}

function Dropdownmenu__trigger_147570026540717198363334525227418462761 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
RadixThemesDropdownMenu.Trigger,
{},
jsx(
RadixThemesButton,
{size:"2",variant:"ghost"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
(isTrue(reflex___state____state__states___auth_state____auth_state.current_user_rx_state_) ? reflex___state____state__states___auth_state____auth_state.current_user_rx_state_?.["full_name"] : "User")
,jsx(LucideChevronDown,{size:16},)
,),),)
  )
}

function Card_296483091012821655871197241601815850444 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesCard,
{css:({ ["width"] : "100%", ["p"] : 4, ["border"] : "2px solid", ["borderColor"] : (isTrue(reflex___state____state__states___clinical_state____clinical_state.dsm5_evaluation_rx_state_?.["criteria_met"]) ? "green.200" : "red.200"), ["background"] : (isTrue(reflex___state____state__states___clinical_state____clinical_state.dsm5_evaluation_rx_state_?.["criteria_met"]) ? "green.50" : "red.50") })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"4"},
jsx(
RadixThemesHeading,
{css:({ ["mb"] : 3 }),size:"5"},
"Evaluation Results"
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"3"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Diagnostic Criteria:"
,),jsx(Badge_251172191373942590356784683588323814514,{},)
,),jsx(
RadixThemesGrid,
{columns:"2",css:({ ["width"] : "100%" }),gap:"4"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold", ["color"] : "gray.700" })},
"Criteria Summary"
,),jsx(Text_145302368150489342538234271523397292048,{},)
,jsx(Text_239596050178459519438495520348781236428,{},)
,jsx(Text_240095476290627264844530266124211513421,{},)
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold", ["color"] : "gray.700" })},
"Confidence Metrics"
,),jsx(Text_30418560931415594894753637272072919251,{},)
,jsx(Fragment_118538740929249036182536401709017576771,{},)
,),),jsx(Fragment_44837027506772755226363404212819306791,{},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold", ["color"] : "blue.700" })},
"Clinical Recommendations"
,),jsx(Fragment_43133945515109735417154123588659805497,{},)
,),),)
  )
}

function Text_3043557093842810840467751045346406345 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold", ["fontSize"] : "lg" })},
reflex___state____state__states___patient_state____patient_state.current_patient_rx_state_?.["name"]
,)
  )
}

function Text_48720832473938332111969035618750431193 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
reflex___state____state__states___patient_state____patient_state.success_message_rx_state_
,)
  )
}

function Fragment_308844573413634321397776449436227436498 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___clinical_state____clinical_state.dsm5_evaluation_rx_state_) ? (jsx(
Fragment,
{},
jsx(Card_296483091012821655871197241601815850444,{},)
,)) : (jsx(Fragment,{},)
)),)
  )
}

function Fragment_118538740929249036182536401709017576771 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue((isTrue(reflex___state____state__states___clinical_state____clinical_state.dsm5_evaluation_rx_state_?.["duration_required"]) ? reflex___state____state__states___clinical_state____clinical_state.dsm5_evaluation_rx_state_?.["duration_required"] : null)) ? (jsx(
Fragment,
{},
jsx(
RadixThemesText,
{as:"p"},
"Duration Required: Yes"
,),)) : (jsx(
Fragment,
{},
jsx(
RadixThemesText,
{as:"p"},
"Duration Required: No"
,),))),)
  )
}

function Text_125797025753458818487309627711841201451 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "blue.600" })},
reflex___state____state__states___clinical_state____clinical_state.required_criteria_status_rx_state_
,)
  )
}

function Badge_251172191373942590356784683588323814514 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesBadge,
{color:(isTrue(reflex___state____state__states___clinical_state____clinical_state.dsm5_evaluation_rx_state_?.["criteria_met"]) ? "green" : "red"),size:"3"},
(isTrue(reflex___state____state__states___clinical_state____clinical_state.dsm5_evaluation_rx_state_?.["criteria_met"]) ? "CRITERIA MET" : "CRITERIA NOT MET")
,)
  )
}

function Debounceinput_262562937531728043971569551798756721694 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_change_9ef0fc2afb99d491ee2a8de005c52620 = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___clinical_state____clinical_state.update_assessment_form", ({ ["field"] : "assessment_date", ["value"] : _e["target"]["value"] }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(DebounceInput,{css:({ ["width"] : "100%" }),debounceTimeout:300,element:RadixThemesTextField.Root,onChange:on_change_9ef0fc2afb99d491ee2a8de005c52620,type:"date",value:(isNotNullOrUndefined(reflex___state____state__states___clinical_state____clinical_state.assessment_form_rx_state_["assessment_date"]) ? reflex___state____state__states___clinical_state____clinical_state.assessment_form_rx_state_["assessment_date"] : "")},)

  )
}

function Fragment_43133945515109735417154123588659805497 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___clinical_state____clinical_state.dsm5_evaluation_rx_state_?.["criteria_met"]) ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "green.600" })},
"\u2713 Diagnostic criteria are met for this disorder"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "blue.600" })},
"\u2022 Consider differential diagnoses"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "blue.600" })},
"\u2022 Evaluate for comorbid conditions"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "blue.600" })},
"\u2022 Assess functional impairment"
,),),)) : (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "red.600" })},
"\u2717 Diagnostic criteria are not fully met"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "blue.600" })},
"\u2022 Consider other disorders in differential"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "blue.600" })},
"\u2022 Re-evaluate symptoms over time"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "blue.600" })},
"\u2022 Consider subsyndromal presentations"
,),),))),)
  )
}

function Dropdownmenu__item_53865495627125640446257350871203294164 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_57e4c3f640fd5e0b686ad03b43a436fd = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___auth_state____auth_state.logout", ({  }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesDropdownMenu.Item,
{css:({ ["color"] : "red.600" }),onClick:on_click_57e4c3f640fd5e0b686ad03b43a436fd},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideLogOut,{size:16},)
,jsx(
RadixThemesText,
{as:"p"},
"Logout"
,),),)
  )
}

function Button_338700933386685087323273182333731322693 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_5fdb76a5d1cc08ee04427c50c8997f06 = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___clinical_state____clinical_state.create_assessment", ({  }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{color:"green",loading:reflex___state____state__states___clinical_state____clinical_state.is_loading_rx_state_,onClick:on_click_5fdb76a5d1cc08ee04427c50c8997f06,size:"3"},
"Complete Assessment"
,)
  )
}

function Button_169816151628875222609663720048451338759 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_e702a5da1364a6fc9edb845ecb9a4cfa = useCallback(((_e) => (addEvents([(Event("_redirect", ({ ["path"] : "/login", ["external"] : false, ["replace"] : false }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{color:"blue",onClick:on_click_e702a5da1364a6fc9edb845ecb9a4cfa},
"Go to Login"
,)
  )
}

function Fragment_66180594422720524184428071614418237441 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___clinical_state____clinical_state.disorder_criteria_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"6"},
jsx(
RadixThemesCard,
{css:({ ["width"] : "100%", ["p"] : 4, ["background"] : "blue.50" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(Heading_156982111666106264475202724075184048066,{},)
,jsx(Text_124410050105615155824358786319773704031,{},)
,jsx(Text_179648672776200129942498047795827996331,{},)
,jsx(Fragment_291002643453393394795517824736366687605,{},)
,),),jsx(
RadixThemesCard,
{css:({ ["width"] : "100%", ["p"] : 4, ["background"] : "gray.50" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Diagnostic Progress"
,),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(Text_125797025753458818487309627711841201451,{},)
,),jsx(Progress_40759411031450424366480919784502127065,{},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "medium" })},
"Confidence Level:"
,),jsx(Badge_176058538072098687603084575584034382682,{},)
,),),),jsx(Flex_331207753910449632678560541778733323758,{},)
,jsx(Fragment_308844573413634321397776449436227436498,{},)
,),)) : (jsx(Fragment,{},)
)),)
  )
}

function Fragment_252584447231808486301477474528160783206 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
Fragment,
{},
(reflex___state____state__states___auth_state____auth_state.is_authenticated_rx_state_ ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"6"},
jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.700", ["fontWeight"] : "medium", ["&:hover"] : ({ ["color"] : "blue.600" }), ["&:active"] : ({ ["color"] : "blue.600", ["fontWeight"] : "bold" }) })},
jsx(
ReactRouterLink,
{to:"/dashboard"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideHome,{size:18},)
,jsx(
RadixThemesText,
{as:"p"},
"Dashboard"
,),),),),jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.700", ["fontWeight"] : "medium", ["&:hover"] : ({ ["color"] : "blue.600" }), ["&:active"] : ({ ["color"] : "blue.600", ["fontWeight"] : "bold" }) })},
jsx(
ReactRouterLink,
{to:"/patients"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideUsers,{size:18},)
,jsx(
RadixThemesText,
{as:"p"},
"Patients"
,),),),),jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.700", ["fontWeight"] : "medium", ["&:hover"] : ({ ["color"] : "blue.600" }), ["&:active"] : ({ ["color"] : "blue.600", ["fontWeight"] : "bold" }) })},
jsx(
ReactRouterLink,
{to:"/assessments"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideClipboardList,{size:18},)
,jsx(
RadixThemesText,
{as:"p"},
"Assessments"
,),),),),jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.700", ["fontWeight"] : "medium", ["&:hover"] : ({ ["color"] : "blue.600" }), ["&:active"] : ({ ["color"] : "blue.600", ["fontWeight"] : "bold" }) })},
jsx(
ReactRouterLink,
{to:"/reports"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideBarChart,{size:18},)
,jsx(
RadixThemesText,
{as:"p"},
"Reports"
,),),),),jsx(Fragment_166734300427242336168950855614206449165,{},)
,),)) : (jsx(Fragment,{},)
)),)
  )
}

export default function Component() {
    




  return (
    jsx(
Fragment,
{},
jsx(Fragment_216061012743779624452318208511252989372,{},)
,jsx(
"title",
{},
"Clinical Assessment - Psychiatry EMR"
,),jsx("meta",{content:"favicon.ico",property:"og:image"},)
,)
  )
}
